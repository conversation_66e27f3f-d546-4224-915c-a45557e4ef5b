/**
 * User Model Module
 *
 * Comprehensive user data model for the StalkAPI system providing user
 * authentication, authorization, and account management functionality.
 * Handles user registration, login, API key management, and credit tracking.
 *
 * Features:
 * - Secure password hashing with bcrypt
 * - API key generation and validation
 * - Credit balance management and tracking
 * - User tier and permission management
 * - Account activation and deactivation
 * - Login tracking and session management
 * - Redis caching for performance optimization
 * - Database interaction with prepared statements
 * - Usage statistics and analytics
 *
 * Database Schema:
 * - id: Primary key (UUID)
 * - email: Unique email address for authentication
 * - password_hash: Bcrypt hashed password
 * - api_key: Unique API key for programmatic access
 * - tier_id: Foreign key to access_tiers table
 * - credits_remaining: Current credit balance
 * - credits_used_this_month: Monthly usage tracking
 * - total_credits_purchased: Lifetime credit purchases
 * - is_active: Account status flag
 * - created_at: Account creation timestamp
 * - updated_at: Last modification timestamp
 * - last_login: Last successful login timestamp
 *
 * Tier Integration:
 * - tier_name: Access tier display name
 * - max_credits_per_month: Monthly credit allowance
 * - max_requests_per_minute: Rate limiting threshold
 * - max_websocket_connections: Concurrent connection limit
 * - allowed_endpoints: Permitted API endpoints array
 * - allowed_streams: Permitted WebSocket streams array
 * - tier_is_enabled: Tier availability status
 *
 * @module User
 * <AUTHOR> Team
 * @version 1.0.0
 */

import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

/**
 * User Model Class
 *
 * Represents a user account in the StalkAPI system with comprehensive
 * authentication, authorization, and account management capabilities.
 * Provides both instance methods for user operations and static methods
 * for user lookup and creation.
 */
export class User {
    /**
     * Create a User instance
     *
     * @param {Object} data - User data object from database
     * @param {string} data.id - User UUID
     * @param {string} data.email - User email address
     * @param {string} data.password_hash - Bcrypt hashed password
     * @param {string} data.api_key - Unique API key
     * @param {number} data.tier_id - Access tier ID
     * @param {number} data.credits_remaining - Current credit balance
     * @param {number} data.credits_used_this_month - Monthly usage
     * @param {number} data.total_credits_purchased - Lifetime purchases
     * @param {boolean} data.is_active - Account status
     * @param {Date} data.last_login - Last login timestamp
     * @param {Date} data.created_at - Creation timestamp
     * @param {Date} data.updated_at - Last update timestamp
     * @param {string} data.tier_name - Access tier name
     * @param {number} data.max_credits_per_month - Monthly credit limit
     * @param {number} data.max_requests_per_minute - Rate limit
     * @param {number} data.max_websocket_connections - Connection limit
     * @param {Array} data.allowed_endpoints - Permitted endpoints
     * @param {Array} data.allowed_streams - Permitted streams
     * @param {boolean} data.is_enabled - Tier enabled status
     */
    constructor(data) {
        // Core user properties
        this.id = data.id;                                          // User UUID
        this.email = data.email;                                    // Email address
        this.password_hash = data.password_hash;                    // Bcrypt hash
        this.api_key = data.api_key;                               // API key
        this.tier_id = data.tier_id;                               // Access tier
        this.credits_remaining = data.credits_remaining;            // Credit balance
        this.credits_used_this_month = data.credits_used_this_month; // Monthly usage
        this.total_credits_purchased = data.total_credits_purchased; // Lifetime purchases
        this.is_active = data.is_active;                           // Account status
        this.last_login = data.last_login;                         // Last login
        this.created_at = data.created_at;                         // Creation date
        this.updated_at = data.updated_at;                         // Last update

        // Tier-related properties (joined from access_tiers table)
        this.tier_name = data.tier_name;                           // Tier display name
        this.max_credits_per_month = data.max_credits_per_month;   // Monthly limit
        this.max_requests_per_minute = data.max_requests_per_minute; // Rate limit
        this.max_websocket_connections = data.max_websocket_connections; // Connection limit
        this.allowed_endpoints = data.allowed_endpoints;           // Permitted endpoints
        this.allowed_streams = data.allowed_streams;               // Permitted streams
        this.tier_is_enabled = data.is_enabled;                    // Tier status
    }

    // Create a new user
    static async create(userData) {
        try {
            const { email, password, tier_id = 1 } = userData;
            
            // Check if user already exists
            const existingUser = await this.findByEmail(email);
            if (existingUser) {
                throw new Error('User already exists with this email');
            }
            
            // Hash password
            const password_hash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS) || 12);
            
            // Generate API key
            const api_key = this.generateApiKey();
            
            // Get tier information to set initial credits (only enabled tiers)
            const tierResult = await query(
                'SELECT max_credits_per_month FROM access_tiers WHERE id = $1 AND is_enabled = true',
                [tier_id]
            );

            if (tierResult.rows.length === 0) {
                throw new Error('Invalid tier ID or tier is disabled');
            }
            
            const maxCredits = tierResult.rows[0].max_credits_per_month;
            const initialCredits = maxCredits === -1 ? 0 : maxCredits; // Unlimited tier gets 0, others get their monthly limit
            
            // Insert user
            const result = await query(
                `INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining)
                 VALUES ($1, $2, $3, $4, $5)
                 RETURNING *`,
                [email, password_hash, api_key, tier_id, initialCredits]
            );
            
            return new User(result.rows[0]);
            
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    // Find user by ID
    static async findById(id) {
        try {
            // Check cache first
            const cacheKey = `user:${id}`;
            let userData = await cache.get(cacheKey);
            
            if (!userData) {
                const result = await query(
                    `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                            at.max_requests_per_minute, at.max_websocket_connections,
                            at.allowed_endpoints, at.allowed_streams, at.is_enabled
                     FROM users u
                     JOIN access_tiers at ON u.tier_id = at.id
                     WHERE u.id = $1 AND at.is_enabled = true`,
                    [id]
                );
                
                if (result.rows.length === 0) {
                    return null;
                }
                
                userData = result.rows[0];
                await cache.set(cacheKey, userData, 300); // Cache for 5 minutes
            }
            
            return new User(userData);
            
        } catch (error) {
            console.error('Error finding user by ID:', error);
            return null;
        }
    }

    // Find user by email
    static async findByEmail(email) {
        try {
            const result = await query(
                `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                        at.max_requests_per_minute, at.max_websocket_connections,
                        at.allowed_endpoints, at.allowed_streams, at.is_enabled
                 FROM users u
                 JOIN access_tiers at ON u.tier_id = at.id
                 WHERE u.email = $1 AND at.is_enabled = true`,
                [email]
            );
            
            if (result.rows.length === 0) {
                return null;
            }
            
            return new User(result.rows[0]);
            
        } catch (error) {
            console.error('Error finding user by email:', error);
            return null;
        }
    }

    // Find user by API key
    static async findByApiKey(apiKey) {
        try {
            // Check cache first
            const cacheKey = `apikey:${apiKey}`;
            let userData = await cache.get(cacheKey);
            
            if (!userData) {
                const result = await query(
                    `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                            at.max_requests_per_minute, at.max_websocket_connections,
                            at.allowed_endpoints, at.allowed_streams, at.is_enabled
                     FROM users u
                     JOIN access_tiers at ON u.tier_id = at.id
                     WHERE u.api_key = $1 AND u.is_active = true AND at.is_enabled = true`,
                    [apiKey]
                );
                
                if (result.rows.length === 0) {
                    return null;
                }
                
                userData = result.rows[0];
                await cache.set(cacheKey, userData, 300); // Cache for 5 minutes
            }
            
            return new User(userData);
            
        } catch (error) {
            console.error('Error finding user by API key:', error);
            return null;
        }
    }

    // Authenticate user with email and password
    static async authenticate(email, password) {
        try {
            const user = await this.findByEmail(email);
            if (!user) {
                return null;
            }
            
            const isValid = await bcrypt.compare(password, user.password_hash);
            if (!isValid) {
                return null;
            }
            
            // Update last login
            await query(
                'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
                [user.id]
            );
            
            // Invalidate cache
            await cache.del(`user:${user.id}`);
            
            return user;
            
        } catch (error) {
            console.error('Error authenticating user:', error);
            return null;
        }
    }

    // Note: JWT token generation removed - API now uses only API key authentication

    // Generate API key
    static generateApiKey() {
        return crypto.randomBytes(parseInt(process.env.API_KEY_LENGTH) || 32).toString('hex');
    }

    // Update user
    async update(updateData) {
        try {
            const allowedFields = ['email', 'tier_id', 'is_active'];
            const updates = [];
            const values = [];
            let paramCount = 1;
            
            for (const [key, value] of Object.entries(updateData)) {
                if (allowedFields.includes(key)) {
                    updates.push(`${key} = $${paramCount}`);
                    values.push(value);
                    paramCount++;
                }
            }
            
            if (updates.length === 0) {
                throw new Error('No valid fields to update');
            }
            
            values.push(this.id);
            
            const result = await query(
                `UPDATE users SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = $${paramCount} RETURNING *`,
                values
            );
            
            if (result.rows.length === 0) {
                throw new Error('User not found');
            }
            
            // Update instance properties
            Object.assign(this, result.rows[0]);
            
            // CRITICAL FIX: Invalidate all related caches including rate limiting
            await cache.del(`user:${this.id}`);
            await cache.del(`apikey:${this.api_key}`);

            // If tier_id was updated, clear rate limiting caches
            if (updateData.tier_id !== undefined) {
                const { redis } = await import('../config/redis.js');
                const rateLimitKeys = await redis.keys(`rate_limit:user:${this.id}*`);
                if (rateLimitKeys.length > 0) {
                    await redis.del(...rateLimitKeys);
                    console.log(`🧹 Cleared ${rateLimitKeys.length} rate limit caches for user ${this.id} after tier change`);
                }
            }
            
            return this;
            
        } catch (error) {
            console.error('Error updating user:', error);
            throw error;
        }
    }

    // Change password
    async changePassword(newPassword) {
        try {
            const password_hash = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS) || 12);
            
            await query(
                'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
                [password_hash, this.id]
            );
            
            this.password_hash = password_hash;
            
            // Invalidate cache
            await cache.del(`user:${this.id}`);
            
            return true;
            
        } catch (error) {
            console.error('Error changing password:', error);
            return false;
        }
    }

    // Regenerate API key
    async regenerateApiKey() {
        try {
            const oldApiKey = this.api_key;
            const newApiKey = User.generateApiKey();
            
            await query(
                'UPDATE users SET api_key = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
                [newApiKey, this.id]
            );
            
            this.api_key = newApiKey;
            
            // Invalidate cache
            await cache.del(`user:${this.id}`);
            await cache.del(`apikey:${oldApiKey}`);
            
            return newApiKey;
            
        } catch (error) {
            console.error('Error regenerating API key:', error);
            return null;
        }
    }

    // Get user's usage statistics
    async getUsageStats(days = 30) {
        try {
            const result = await query(
                `SELECT 
                    COUNT(*) as total_requests,
                    SUM(credits_consumed) as total_credits_consumed,
                    AVG(response_time_ms) as avg_response_time,
                    COUNT(DISTINCT endpoint) as unique_endpoints,
                    DATE_TRUNC('day', created_at) as date,
                    COUNT(*) as daily_requests
                 FROM api_usage_logs 
                 WHERE user_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
                 GROUP BY DATE_TRUNC('day', created_at)
                 ORDER BY date DESC`,
                [this.id]
            );
            
            return result.rows;
            
        } catch (error) {
            console.error('Error getting usage stats:', error);
            return [];
        }
    }

    // Serialize user data (remove sensitive information)
    toJSON() {
        const { password_hash, ...userData } = this;
        return userData;
    }
}
