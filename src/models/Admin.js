/**
 * Admin Model Module
 *
 * Comprehensive administrative user model for the StalkAPI system providing
 * elevated access control, permission management, and administrative operations.
 * Handles admin authentication, authorization, and secure credential management.
 *
 * Features:
 * - API key-based authentication for programmatic access
 * - Granular permission system with role-based access control
 * - Account activation and deactivation management
 * - Usage tracking and session management
 * - Database interaction with prepared statements
 * - Redis caching for performance optimization
 * - Comprehensive audit logging capabilities
 *
 * Database Schema:
 * - id: Primary key (UUID)
 * - name: Display name for admin user
 * - email: Unique email address for authentication
 * - api_key: Unique API key for programmatic access
 * - permissions: JSON array of granted permissions
 * - is_active: Account status flag
 * - created_at: Account creation timestamp
 * - updated_at: Last modification timestamp
 * - last_used: Last activity timestamp
 *
 * Permission System:
 * - users:read - View user accounts and information
 * - users:write - Create, update, and manage user accounts
 * - tiers:read - View access tier configurations
 * - tiers:write - Modify access tier settings
 * - system:admin - Full system administration access
 * - credits:manage - Credit and billing management
 * - analytics:read - Access to system analytics and reports
 * - logs:read - Access to system logs and audit trails
 *
 * Security Features:
 * - Secure API key generation with cryptographic randomness
 * - Permission-based access control for all operations
 * - Account lockout and activation controls
 * - Comprehensive audit logging for compliance
 * - Session management and timeout controls
 *
 * @module Admin
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

/**
 * Admin Model Class
 *
 * Represents an administrative user in the StalkAPI system with elevated
 * privileges and comprehensive permission management capabilities.
 * Provides both instance methods for admin operations and static methods
 * for admin lookup and creation.
 */
export class Admin {
    /**
     * Create an Admin instance
     *
     * @param {Object} data - Admin data object from database
     * @param {string} data.id - Admin UUID
     * @param {string} data.name - Admin display name
     * @param {string} data.email - Admin email address
     * @param {string} data.api_key - Unique API key
     * @param {Array} data.permissions - Array of granted permissions
     * @param {boolean} data.is_active - Account status
     * @param {Date} data.created_at - Creation timestamp
     * @param {Date} data.updated_at - Last update timestamp
     * @param {Date} data.last_used - Last activity timestamp
     */
    constructor(data) {
        this.id = data.id;                              // Admin UUID
        this.name = data.name;                          // Display name
        this.email = data.email;                        // Email address
        this.api_key = data.api_key;                   // API key
        this.permissions = data.permissions || [];      // Permission array
        this.is_active = data.is_active;               // Account status
        this.created_at = data.created_at;             // Creation date
        this.updated_at = data.updated_at;             // Last update
        this.last_used = data.last_used;               // Last activity
    }

    /**
     * Find admin by API key
     * @param {string} apiKey - Admin API key
     * @returns {Admin|null} - Admin instance or null
     */
    static async findByApiKey(apiKey) {
        if (!apiKey) {
            return null;
        }

        try {
            // Check cache first
            const cacheKey = `admin:apikey:${apiKey}`;
            const cached = await cache.get(cacheKey);
            
            if (cached) {
                return new Admin(JSON.parse(cached));
            }

            // Query database
            const result = await query(
                `SELECT * FROM admin_users WHERE api_key = $1 AND is_active = true`,
                [apiKey]
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Cache for 10 minutes
            await cache.set(cacheKey, JSON.stringify(admin), 'EX', 600);

            // Update last_used timestamp
            await query(
                'UPDATE admin_users SET last_used = CURRENT_TIMESTAMP WHERE id = $1',
                [admin.id]
            );

            return admin;

        } catch (error) {
            console.error('Error finding admin by API key:', error);
            return null;
        }
    }

    /**
     * Find admin by ID
     * @param {string} id - Admin ID
     * @returns {Admin|null} - Admin instance or null
     */
    static async findById(id) {
        if (!id) {
            return null;
        }

        try {
            // Check cache first
            const cacheKey = `admin:id:${id}`;
            const cached = await cache.get(cacheKey);
            
            if (cached) {
                return new Admin(JSON.parse(cached));
            }

            // Query database
            const result = await query(
                'SELECT * FROM admin_users WHERE id = $1 AND is_active = true',
                [id]
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Cache for 10 minutes
            await cache.set(cacheKey, JSON.stringify(admin), 'EX', 600);

            return admin;

        } catch (error) {
            console.error('Error finding admin by ID:', error);
            return null;
        }
    }

    /**
     * Check if admin has specific permission
     * @param {string} permission - Permission to check (e.g., 'tiers:write')
     * @returns {boolean} - True if admin has permission
     */
    hasPermission(permission) {
        if (!this.is_active) {
            return false;
        }

        // System admin has all permissions
        if (this.permissions.includes('system:admin')) {
            return true;
        }

        // Check specific permission
        return this.permissions.includes(permission);
    }

    /**
     * Check if admin has any of the specified permissions
     * @param {string[]} permissions - Array of permissions to check
     * @returns {boolean} - True if admin has any of the permissions
     */
    hasAnyPermission(permissions) {
        if (!this.is_active) {
            return false;
        }

        // System admin has all permissions
        if (this.permissions.includes('system:admin')) {
            return true;
        }

        // Check if admin has any of the specified permissions
        return permissions.some(permission => this.permissions.includes(permission));
    }

    /**
     * Get all admins
     * @returns {Admin[]} - Array of admin instances
     */
    static async getAll() {
        try {
            const result = await query(
                'SELECT * FROM admin_users ORDER BY created_at DESC'
            );

            return result.rows.map(row => new Admin(row));

        } catch (error) {
            console.error('Error getting all admins:', error);
            return [];
        }
    }

    /**
     * Create new admin
     * @param {Object} adminData - Admin data
     * @returns {Admin|null} - Created admin instance or null
     */
    static async create(adminData) {
        const { name, email, api_key, permissions = [] } = adminData;

        try {
            const result = await query(
                `INSERT INTO admin_users (name, email, api_key, permissions)
                 VALUES ($1, $2, $3, $4)
                 RETURNING *`,
                [name, email, api_key, permissions]
            );

            const admin = new Admin(result.rows[0]);

            // Clear cache
            await this.clearCache(admin.api_key, admin.id);

            return admin;

        } catch (error) {
            console.error('Error creating admin:', error);
            return null;
        }
    }

    /**
     * Update admin
     * @param {string} id - Admin ID
     * @param {Object} updateData - Data to update
     * @returns {Admin|null} - Updated admin instance or null
     */
    static async update(id, updateData) {
        const allowedFields = ['name', 'email', 'permissions', 'is_active'];
        const updates = [];
        const values = [];
        let paramCount = 1;

        for (const [key, value] of Object.entries(updateData)) {
            if (allowedFields.includes(key)) {
                updates.push(`${key} = $${paramCount}`);
                values.push(value);
                paramCount++;
            }
        }

        if (updates.length === 0) {
            return null;
        }

        updates.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(id);

        try {
            const result = await query(
                `UPDATE admin_users SET ${updates.join(', ')} WHERE id = $${paramCount} RETURNING *`,
                values
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Clear cache
            await this.clearCache(admin.api_key, admin.id);

            return admin;

        } catch (error) {
            console.error('Error updating admin:', error);
            return null;
        }
    }

    /**
     * Delete admin (soft delete by setting is_active to false)
     * @param {string} id - Admin ID
     * @returns {boolean} - True if deleted successfully
     */
    static async delete(id) {
        try {
            const result = await query(
                'UPDATE admin_users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING api_key',
                [id]
            );

            if (result.rows.length > 0) {
                // Clear cache
                await this.clearCache(result.rows[0].api_key, id);
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error deleting admin:', error);
            return false;
        }
    }

    /**
     * Clear admin cache
     * @param {string} apiKey - Admin API key
     * @param {string} id - Admin ID
     */
    static async clearCache(apiKey, id) {
        try {
            const keys = [];
            
            if (apiKey) {
                keys.push(`admin:apikey:${apiKey}`);
            }
            
            if (id) {
                keys.push(`admin:id:${id}`);
            }

            if (keys.length > 0) {
                await cache.del(...keys);
            }

        } catch (error) {
            console.error('Error clearing admin cache:', error);
        }
    }

    /**
     * Generate secure admin API key
     * @returns {string} - Generated API key
     */
    static generateApiKey() {
        const crypto = require('crypto');
        const prefix = 'admin_';
        const randomBytes = crypto.randomBytes(32).toString('hex');
        return `${prefix}${randomBytes}`;
    }
}
