/**
 * Data Manager - Orchestrates Data Processing Workers
 *
 * The DataManager class serves as the central coordinator for all data processing
 * workers in the StalkAPI system. It manages the lifecycle of background workers
 * that handle scheduled data aggregation, caching, and analysis tasks.
 *
 * Current Workers:
 * - SmartMoney: Aggregates smart money trading data every 15 minutes
 *
 * Future Workers (planned):
 * - TokenMetadata: Refreshes token metadata cache
 * - Analytics: Generates usage analytics and reports
 * - Cleanup: Performs database maintenance and cleanup tasks
 *
 * @class DataManager
 * <AUTHOR> Team
 * @version 1.0.0
 */

import SmartMoney from "./workers/smartMoney.js";

class DataManager {
  /**
   * Initialize DataManager with all worker instances
   *
   * Workers are instantiated but not started until initialize() is called.
   * This allows for proper error handling during startup.
   */
  constructor() {
    this.workers = {
      smartMoney: new SmartMoney(), // Smart money data aggregation worker
    };

    // Track initialization state for proper lifecycle management
    this.isInitialized = false;
  }

  /**
   * Initialize and start all data processing workers
   *
   * Starts workers in sequence to ensure proper initialization order.
   * If any worker fails to initialize, the entire process is aborted
   * to maintain system consistency.
   *
   * @async
   * @throws {Error} If any worker fails to initialize
   */
  async initialize() {
    try {
      console.log("🚀 Initializing Data Manager...");

      // Initialize all workers in sequence
      // TODO: Consider parallel initialization if workers become independent
      await this.workers.smartMoney.init();

      this.isInitialized = true;
      console.log("✅ Data Manager initialized successfully");
    } catch (error) {
      console.error("❌ Error initializing Data Manager:", error);
      this.isInitialized = false;
      throw error; // Re-throw to allow caller to handle initialization failure
    }
  }

  /**
   * Stop all data processing workers gracefully
   *
   * Stops workers in reverse order to ensure proper cleanup.
   * Continues attempting to stop all workers even if some fail,
   * to maximize cleanup coverage.
   *
   * @async
   * @throws {Error} If critical errors occur during shutdown
   */
  async stop() {
    try {
      console.log("🛑 Stopping Data Manager...");

      // Stop workers in reverse order of initialization
      if (this.workers.smartMoney) {
        this.workers.smartMoney.stop();
      }

      this.isInitialized = false;
      console.log("✅ Data Manager stopped successfully");
    } catch (error) {
      console.error("❌ Error stopping Data Manager:", error);
      throw error; // Re-throw to allow caller to handle shutdown failure
    }
  }

  /**
   * Get the current status of all workers
   *
   * @returns {Object} Status information for all workers
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      workers: {
        smartMoney: this.workers.smartMoney?.isRunning || false,
      },
    };
  }
}

export default DataManager;
