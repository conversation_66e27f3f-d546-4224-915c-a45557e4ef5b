/**
 * Credits Middleware Module
 *
 * Comprehensive credit management system for the StalkAPI platform providing
 * usage tracking, consumption control, and billing integration. Implements
 * real-time credit consumption, automatic reversal for failed requests,
 * and detailed usage analytics.
 *
 * Features:
 * - Automatic credit consumption after successful responses
 * - Credit reversal for failed requests and server errors
 * - Multi-tier credit system with unlimited plans
 * - Database and cache synchronization for accuracy
 * - Response time tracking for performance monitoring
 * - Comprehensive error handling and logging
 * - Sensitive data sanitization in usage logs
 *
 * Credit System:
 * - Basic Tier: Limited monthly credits with consumption tracking
 * - Premium Tier: Higher monthly allowance with rollover
 * - Enterprise Tier: Unlimited credits (-1 value)
 * - Admin Users: Bypass all credit checks and consumption
 *
 * Cache Management:
 * - Redis caching for fast credit balance lookups
 * - Automatic cache invalidation on credit updates
 * - Fallback to database for cache misses
 * - Cache synchronization for accuracy
 *
 * Error Handling:
 * - Automatic credit reversal for 5xx server errors
 * - No credit consumption for 4xx client errors
 * - Comprehensive error logging and monitoring
 * - Graceful degradation for system failures
 *
 * @module credits
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

/**
 * Credit Consumption Middleware
 *
 * Automatically consumes credits after successful API responses (2xx status codes).
 * Intercepts the response to determine success/failure and only charges credits
 * for successful requests. Updates response data to include credit consumption info.
 *
 * @param {number} creditsAmount - Number of credits to consume (default: 1)
 * @returns {Function} Express middleware function
 *
 * @middleware
 * @example
 * router.get('/endpoint', consumeCredits(3), async (req, res) => {
 *   // Endpoint logic - 3 credits will be consumed on success
 * });
 */
export const consumeCredits = (creditsAmount = 1) => {
    return async (req, res, next) => {
        // Store credit information in request for later use
        req.creditsToConsume = creditsAmount;
        req.creditsConsumed = false;

        // Store original res.json to intercept response
        const originalJson = res.json;

        res.json = function(data) {
            // Only consume credits on successful responses (2xx status codes)
            if (res.statusCode >= 200 && res.statusCode < 300) {
                req.creditsConsumed = true;
                // Don't await this to avoid blocking the response
                consumeUserCredits(req, res, creditsAmount).catch(error => {
                    console.error('Credit consumption error:', error);
                });

                // Update response data to reflect actual credit consumption
                if (data && typeof data === 'object') {
                    data.credits_consumed = creditsAmount;
                }
            } else {
                // For error responses, ensure credits_consumed reflects reality
                if (data && typeof data === 'object') {
                    data.credits_consumed = 0;
                    data.credit_note = 'No credits charged for server errors';
                }
            }

            // Call original json method
            return originalJson.call(this, data);
        };

        next();
    };
};

// Function to actually consume credits and log usage
const consumeUserCredits = async (req, res, creditsAmount) => {
    try {
        const user = req.user;
        
        // Skip credit consumption for unlimited tier
        if (user.max_credits_per_month === -1) {
            await logApiUsage(req, res, 0);
            return;
        }
        
        const endpoint = req.route?.path || req.path;
        const method = req.method;
        const responseTime = res.get('X-Response-Time') || null;
        const ipAddress = req.realIP || req.ip;
        const userAgent = req.get('User-Agent');
        
        // Prepare request payload (sanitize sensitive data)
        const requestPayload = sanitizePayload(req.body);
        
        // Use database function to consume credits and log usage
        const result = await query(
            'SELECT consume_credits($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)',
            [
                user.id,
                endpoint,
                method,
                creditsAmount,
                res.statusCode,
                responseTime ? parseInt(responseTime) : null,
                ipAddress,
                userAgent,
                requestPayload ? JSON.stringify(requestPayload) : null,
                null // response payload - we'll skip this for now to avoid storing large responses
            ]
        );
        
        const success = result.rows[0].consume_credits;
        
        if (!success) {
            console.error('Failed to consume credits for user:', user.id);
        } else {
            // Update cached user data
            const cacheKey = `user:${user.id}`;
            const cachedUser = await cache.get(cacheKey);
            if (cachedUser) {
                cachedUser.credits_remaining -= creditsAmount;
                cachedUser.credits_used_this_month += creditsAmount;
                await cache.set(cacheKey, cachedUser, 300);
            }
            
            // Also update API key cache if used
            if (req.authMethod === 'apikey') {
                const apiKeyCacheKey = `apikey:${user.api_key}`;
                const cachedApiKeyUser = await cache.get(apiKeyCacheKey);
                if (cachedApiKeyUser) {
                    cachedApiKeyUser.credits_remaining -= creditsAmount;
                    cachedApiKeyUser.credits_used_this_month += creditsAmount;
                    await cache.set(apiKeyCacheKey, cachedApiKeyUser, 300);
                }
            }
        }
        
    } catch (error) {
        console.error('Error consuming credits:', error);
    }
};

// Log API usage without consuming credits
const logApiUsage = async (req, res, creditsAmount = 0) => {
    try {
        const user = req.user;
        const endpoint = req.route?.path || req.path;
        const method = req.method;
        const responseTime = res.get('X-Response-Time') || null;
        const ipAddress = req.realIP || req.ip;
        const userAgent = req.get('User-Agent');
        const requestPayload = sanitizePayload(req.body);
        
        await query(
            `INSERT INTO api_usage_logs (
                user_id, endpoint, method, credits_consumed, response_status,
                response_time_ms, ip_address, user_agent, request_payload
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
            [
                user.id,
                endpoint,
                method,
                creditsAmount,
                res.statusCode,
                responseTime ? parseInt(responseTime) : null,
                ipAddress,
                userAgent,
                requestPayload ? JSON.stringify(requestPayload) : null
            ]
        );
        
    } catch (error) {
        console.error('Error logging API usage:', error);
    }
};

// Sanitize request payload to remove sensitive information
const sanitizePayload = (payload) => {
    if (!payload || typeof payload !== 'object') {
        return payload;
    }
    
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
    const sanitized = { ...payload };
    
    const sanitizeObject = (obj) => {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const lowerKey = key.toLowerCase();
                if (sensitiveFields.some(field => lowerKey.includes(field))) {
                    obj[key] = '[REDACTED]';
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    sanitizeObject(obj[key]);
                }
            }
        }
    };
    
    sanitizeObject(sanitized);
    return sanitized;
};

// Middleware to add response time header
export const responseTime = (req, res, next) => {
    const start = Date.now();

    // Store original res.end to intercept response
    const originalEnd = res.end;

    res.end = function(...args) {
        const duration = Date.now() - start;
        res.set('X-Response-Time', duration.toString());
        return originalEnd.apply(this, args);
    };

    next();
};

// Get user's current credit status
export const getCreditStatus = async (userId) => {
    try {
        const result = await query(
            `SELECT u.credits_remaining, u.credits_used_this_month, u.total_credits_purchased,
                    at.max_credits_per_month, at.name as tier_name
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.id = $1`,
            [userId]
        );
        
        if (result.rows.length === 0) {
            return null;
        }
        
        const user = result.rows[0];
        return {
            credits_remaining: user.credits_remaining,
            credits_used_this_month: user.credits_used_this_month,
            total_credits_purchased: user.total_credits_purchased,
            max_credits_per_month: user.max_credits_per_month,
            tier_name: user.tier_name,
            unlimited: user.max_credits_per_month === -1
        };
        
    } catch (error) {
        console.error('Error getting credit status:', error);
        return null;
    }
};

// Add credits to user account
export const addCreditsToUser = async (userId, creditsAmount, transactionType = 'purchase', description = null, referenceId = null) => {
    try {
        const result = await query(
            'SELECT add_credits($1, $2, $3, $4, $5)',
            [userId, creditsAmount, transactionType, description, referenceId]
        );

        const success = result.rows[0].add_credits;

        if (success) {
            // Invalidate user cache
            await cache.del(`user:${userId}`);

            // Also invalidate API key cache if exists
            const userResult = await query('SELECT api_key FROM users WHERE id = $1', [userId]);
            if (userResult.rows.length > 0) {
                await cache.del(`apikey:${userResult.rows[0].api_key}`);
            }
        }

        return success;

    } catch (error) {
        console.error('Error adding credits:', error);
        return false;
    }
};

// Reverse credit consumption for server errors
export const reverseCredits = async (req, creditsAmount, reason = 'Server error refund') => {
    try {
        const user = req.user;

        if (!user || !user.id) {
            console.error('Cannot reverse credits: No user found in request');
            return false;
        }

        // Skip reversal for unlimited tier
        if (user.max_credits_per_month === -1) {
            console.log(`Credit reversal skipped for unlimited tier user ${user.id}`);
            return true;
        }

        console.log(`🔄 Reversing ${creditsAmount} credits for user ${user.id} - Reason: ${reason}`);

        // Add credits back to user account
        const success = await addCreditsToUser(
            user.id,
            creditsAmount,
            'refund',
            reason,
            `error_refund_${Date.now()}`
        );

        if (success) {
            console.log(`✅ Successfully reversed ${creditsAmount} credits for user ${user.id}`);

            // Update cached user data immediately
            const cacheKey = `user:${user.id}`;
            const cachedUser = await cache.get(cacheKey);
            if (cachedUser) {
                cachedUser.credits_remaining += creditsAmount;
                cachedUser.credits_used_this_month = Math.max(0, cachedUser.credits_used_this_month - creditsAmount);
                await cache.set(cacheKey, cachedUser, 300);
            }

            // Also update API key cache if used
            if (req.authMethod === 'apikey' && user.api_key) {
                const apiKeyCacheKey = `apikey:${user.api_key}`;
                const cachedApiKeyUser = await cache.get(apiKeyCacheKey);
                if (cachedApiKeyUser) {
                    cachedApiKeyUser.credits_remaining += creditsAmount;
                    cachedApiKeyUser.credits_used_this_month = Math.max(0, cachedApiKeyUser.credits_used_this_month - creditsAmount);
                    await cache.set(apiKeyCacheKey, cachedApiKeyUser, 300);
                }
            }
        } else {
            console.error(`❌ Failed to reverse ${creditsAmount} credits for user ${user.id}`);
        }

        return success;

    } catch (error) {
        console.error('Error reversing credits:', error);
        return false;
    }
};

// Middleware to handle credit reversal on server errors
export const handleCreditReversal = (req, res, next) => {
    // Store original res.status to intercept status changes
    const originalStatus = res.status;

    res.status = function(statusCode) {
        // If this is a server error (5xx) and credits were supposed to be consumed
        if (statusCode >= 500 && req.creditsToConsume && !req.creditsConsumed) {
            // Credits weren't consumed due to error, but we should log this for transparency
            console.log(`💳 Credits not consumed due to ${statusCode} error for user ${req.user?.id || 'unknown'}`);
        }

        // Call original status method
        return originalStatus.call(this, statusCode);
    };

    next();
};
