{"openapi": "3.0.3", "info": {"title": "StalkAPI", "description": "StalkAPI provides API access to StalkChain data and analytics. Data is available through both REST API and WebSocket streaming.\n\n## Authentication\nStalkAPI uses API key authentication for all endpoints and WebSocket connections:\n- **API Key**: Generated for each user account and used for all authentication\n- **Header**: `X-API-Key: YOUR_API_KEY`\n- **Query Parameter**: `?apiKey=YOUR_API_KEY`\n\n## Credit System\nMost endpoints consume credits based on your tier:\n- **Free Tier**: 1,000 credits/month ($0.00)\n- **Basic Tier**: 1,000,000 credits/month ($49.99)\n- **Premium Tier**: 5,000,000 credits/month ($149.99)\n- **Enterprise Tier**: Unlimited credits ($499.99)\n\n## Rate Limiting\nAPI endpoints are rate-limited based on your tier to ensure fair usage.\n\n## WebSocket Connection\nReal-time data is available through WebSocket connections at `wss://data.stalkapi.com/ws`\n\n### WebSocket Connection\nConnect to the WebSocket endpoint using your API key:\n```javascript\nconst ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');\n```\n\n### Available Streams\n\n#### Core Streams\n\n**kol-feed** - Real-time KOL trading activity\n- **Tier Required**: Basic+ (Basic, Premium, Enterprise)\n- **Credit Cost**: 2 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"kol-feed\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"kol-feed\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"timestamp\": 1748919450434,\n    \"type\": \"buy\",\n    \"kol_label\": \"TraderSZ\",\n    \"wallet\": \"********************************************\",\n    \"kol_avatar\": \"https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/trader_avatar.jpg\",\n    \"token_in\": {\n      \"symbol\": \"SOL\",\n      \"name\": \"Wrapped SOL\",\n      \"amount\": 0.92,\n      \"amount_usd\": 146.47,\n      \"price\": 159.25\n    },\n    \"token_out\": {\n      \"symbol\": \"BONK\",\n      \"name\": \"Bonk\",\n      \"amount\": 6341.23,\n      \"amount_usd\": 146.47,\n      \"price\": 0.000023\n    },\n    \"socials\": [\n      {\n        \"type\": \"x\",\n        \"handle\": \"trader1sz\",\n        \"followers\": 658700\n      }\n    ],\n    \"signature\": \"5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr\"\n  }\n}\n```\n\n**fresh-wallet-feed** - Real-time fresh wallet trading activity\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"fresh-wallet-feed\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"fresh-wallet-feed\",\n  \"data\": {\n    \"platform\": \"pumpfun\",\n    \"type\": \"buy\",\n    \"timestamp\": \"2025-06-10T11:11:20.000Z\",\n    \"wallet\": \"E3tnve5kXKu8HY9H5gY2rnPtRTSn3PMYHiej5S8D1Xa1\",\n    \"wallet_age\": \"2025-06-10T11:09:38.000Z\",\n    \"wallet_first_activity\": \"2025-06-10T11:09:38.000Z\",\n    \"wallet_transactions_count\": 2,\n    \"wallet_funding_mint\": \"So11111111111111111111111111111111111111112\",\n    \"wallet_funding_amount\": 287.953464859,\n    \"wallet_funding_source\": \"wallet\",\n    \"wallet_funding_source_wallet\": \"BgNaCk7TvfFudwFf6p2JCXMZGRpPgHquUZekpWrNDK7k\",\n    \"wallet_funding_source_name\": \"Unknown\",\n    \"sol_amount\": 3.997950727,\n    \"usd_amount\": 630.148268620624,\n    \"token_in\": {\n      \"mint\": \"So11111111111111111111111111111111111111112\",\n      \"symbol\": \"SOL\",\n      \"name\": \"Wrapped SOL\",\n      \"decimals\": 9,\n      \"logo\": \"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png\",\n      \"amount\": 3.997950727,\n      \"amount_string\": \"3.997950727\",\n      \"amount_usd\": 630.148268620624,\n      \"price\": 157.61781763965797\n    },\n    \"token_out\": {\n      \"mint\": \"ApFTK9kFEkG524Aojza9Ppg5r3CuacEi86cECjunQV4Z\",\n      \"symbol\": \"farmcoin\",\n      \"name\": \"farmcoin\",\n      \"decimals\": 9,\n      \"logo\": \"https://ipfs.io/ipfs/QmZpWoTpewWbt2DH46JiQZJ4tDEgaTdesguX5hwSfPku7M\",\n      \"amount\": 6470.964971831,\n      \"amount_string\": \"6470.964971831\",\n      \"amount_usd\": 630.148268620624,\n      \"price\": 0.00009332343535253934\n    },\n    \"signature\": \"2FfC5wqAZKcT1Z8ZWJ9TBbAg4wfSfm5W4vEUN7qrWNTmV8LhFu5r6HXnFdbSjeH6hMvLPbar6qrNVqHqcRTg8YBS\"\n  },\n  \"timestamp\": 1749553886053\n}\n```\n\n**jupiter-amm-swaps** - Real-time Jupiter AMM swap data\n- **Tier Required**: Enterprise\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"jupiter-amm-swaps\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"jupiter-amm-swaps\",\n  \"timestamp\": 1749105627439,\n  \"data\": {\n    \"timestamp\": 1749105624,\n    \"source\": \"jupiter-amm-swaps\",\n    \"signature\": \"4XMZXvg6M719TdbgedfVuGAXrDMHjLbDToJrce3sWYYKkM18zp6Ze5pyNrJUtmjxtaNZ72GUeKkGxJM7ah2bqQY2\",\n    \"wallet\": \"43xFFxN7npfARaWVxiRNaE6KZX6NnxjyUVkrme8eLBcT\",\n    \"slot\": \"344727508\",\n    \"swap\": {\n      \"input_mint\": \"So11111111111111111111111111111111111111112\",\n      \"input_amount\": \"280643341\",\n      \"input_decimals\": 9,\n      \"output_mint\": \"G9Ym6ngYBAHTP6MeydoA4TfbkSShjmapPGyXAG6opump\",\n      \"output_amount\": \"411900774673\",\n      \"output_decimals\": 6,\n      \"price\": 6.813372497849689e-7,\n      \"price_inverse\": 1467701.9351512068\n    },\n    \"child_routes\": [\n      {\n        \"amm\": \"pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA\",\n        \"input_mint\": \"So11111111111111111111111111111111111111112\",\n        \"input_amount\": \"280643341\",\n        \"input_decimals\": 9,\n        \"output_mint\": \"G9Ym6ngYBAHTP6MeydoA4TfbkSShjmapPGyXAG6opump\",\n        \"output_amount\": \"411900774673\",\n        \"output_decimals\": 6\n      }\n    ],\n    \"fee\": null,\n    \"type\": \"jupiter_amm_swap\"\n  }\n}\n```\n\n**pumpfun-amm-swaps** - Real-time Pump.fun AMM swap data\n- **Tier Required**: Enterprise\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"pumpfun-amm-swaps\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"pumpfun-amm-swaps\",\n  \"timestamp\": 1749105656771,\n  \"data\": {\n    \"timestamp\": \"1749105652\",\n    \"source\": \"pumpfun-amm-swaps\",\n    \"type\": \"pumpfun_amm_swap\",\n    \"signature\": \"2324vbme4ToaMbQp6fN9j2vVKkPLBLHaA9LUfjur4A9PdbKxcXsV3oM4mogp5Tp9jRnct59xCxRWLmHpAnBr9E9M\",\n    \"slot\": \"344727583\",\n    \"is_amm\": false,\n    \"amm\": null,\n    \"fishy_transaction\": true,\n    \"tokens\": {\n      \"BmyVLpEnWNHGf8GEmtT5XdJ2kZ5hUUbWkruGzq8RvjW7\": 6,\n      \"So11111111111111111111111111111111111111112\": 9\n    },\n    \"event_data\": {\n      \"timestamp\": \"1749105652\",\n      \"base_amount_in\": \"1879900705\",\n      \"min_quote_amount_out\": \"1717977006460\",\n      \"user_base_token_reserves\": \"1879900705\",\n      \"user_quote_token_reserves\": \"68633175798\",\n      \"pool_base_token_reserves\": \"829146469483\",\n      \"pool_quote_token_reserves\": \"***************\",\n      \"quote_amount_out\": \"*************\",\n      \"lp_fee_basis_points\": \"20\",\n      \"lp_fee\": \"**********\",\n      \"protocol_fee_basis_points\": \"5\",\n      \"protocol_fee\": \"*********\",\n      \"quote_amount_out_without_lp_fee\": \"*************\",\n      \"user_quote_amount_out\": \"*************\",\n      \"pool\": \"B9EsQyACoVWfsQKhxaReouskhcAhrLTtRj3dNSs3nts3\",\n      \"user\": \"FmDgKzd6FNp4iYV3PjgJUUWFc9mf3Ei5XsnqwJSL3bR1\",\n      \"user_base_token_account\": \"Byz8Xysm4jiy2eJX9NSTeGqDhJLmPbXFPtFAUq9pd9ja\",\n      \"user_quote_token_account\": \"6pjHnHK8H5Mz56iUu2DJKwxZoD3QpFEJrKk23EwEL1jJ\",\n      \"protocol_fee_recipient\": \"7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ\",\n      \"protocol_fee_recipient_token_account\": \"14cY9fD4H3cxxphiQoS6MjC6sdHY7VDNGdDHhsxmXRRg\"\n    }\n  }\n}\n```\n\n**jupiter-dca-orders** - Real-time Jupiter DCA order data\n- **Tier Required**: Enterprise\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"jupiter-dca-orders\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"jupiter-dca-orders\",\n  \"timestamp\": *************,\n  \"data\": {\n    \"timestamp\": **********,\n    \"source\": \"jupiter-dca-orders\",\n    \"event_type\": \"opened\",\n    \"slot\": \"*********\",\n    \"datetime\": \"2025-06-05T06:43:28.000Z\",\n    \"signature\": \"38z4DXNYVKHL2wzPvKkEMeUaQtzz1tga3QZZUY6VFXtFTHAJsJXvrtuFGKgPAFXRJjW8v91RsDLpdbUuxvKcdNdW\",\n    \"user_key\": \"Ba2p2RTZwdm2Xu3z55L33jT7qVX6N2ioqavoTVvi14ho\",\n    \"dca_key\": \"Cz6rWudGM5CE5cdsNoditWQEogjmPokx4u42Y6phVujK\",\n    \"in_deposited\": 4.93,\n    \"input_mint\": \"So11111111111111111111111111111111111111112\",\n    \"output_mint\": \"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v\",\n    \"input_decimals\": 9,\n    \"output_decimals\": 6,\n    \"cycle_frequency\": 60,\n    \"in_amount_per_cycle\": 1.2325,\n    \"created_at\": 1749105807,\n    \"input_usd_price\": 152.70851812008635,\n    \"output_usd_price\": 1,\n    \"in_deposited_usd\": 752.8529943320257,\n    \"type\": \"jupiter_dca_order\"\n  }\n}\n```\n\n#### Premium Streams\n\n**Basic Streams (No Parameters Required):**\n\n**tokens-launched** - Latest tokens and pools launched on Solana\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"tokens-launched\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"tokens-launched\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"symbol\": \"TEST\",\n    \"name\": \"Test Token\",\n    \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\",\n    \"decimals\": 6,\n    \"has_metadata_file\": true,\n    \"metadata\": {\n      \"name\": \"Test Token\",\n      \"symbol\": \"TEST\",\n      \"uri\": \"https://example.com/metadata.json\"\n    },\n    \"launched_on\": \"2024-01-15T10:30:00Z\",\n    \"risk\": {\n      \"score\": 75,\n      \"risks\": [\n        {\n          \"name\": \"Low Liquidity\",\n          \"description\": \"Token has low liquidity\",\n          \"level\": \"medium\",\n          \"score\": 25\n        }\n      ]\n    },\n    \"pools\": [\n      {\n        \"pool_id\": \"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2\",\n        \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\",\n        \"market_cap\": 1250000,\n        \"liquidity\": 125000.50,\n        \"price\": 0.000023,\n        \"decimals\": 6,\n        \"token_supply\": 1000000000,\n        \"liquidity_pool_burned\": 95.5,\n        \"market\": \"pump.fun\",\n        \"quote_token\": \"SOL\",\n        \"bonding_curve\": \"6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P\",\n        \"curve_percentage\": 85.2,\n        \"freeze_authority\": null,\n        \"mint_authority\": null,\n        \"deployer\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\",\n        \"created_at\": \"2024-01-15T10:30:00Z\",\n        \"updated_at\": \"2024-01-15T10:30:00Z\"\n      }\n    ]\n  }\n}\n```\n\n**tokens-graduating** - Tokens approaching graduation on Pump.fun/Moonshot\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: market_cap (Optional: minimum market cap threshold, e.g., 175 for $175k)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"tokens-graduating\"\n  }\n}\n```\n\n**Parameterized Subscription (Optional):**\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"tokens-graduating\",\n    \"parameters\": {\n      \"market_cap\": 175\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"tokens-graduating\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"symbol\": \"INTERN\",\n    \"name\": \"AINTERN\",\n    \"mint\": \"2sHTXKa4orHCk5GoXc6BVueDdrKnXeaWPuNDC2KTpump\",\n    \"decimals\": 6,\n    \"has_metadata_file\": true,\n    \"metadata\": {\n      \"name\": \"AINTERN\",\n      \"symbol\": \"INTERN\",\n      \"description\": \"AI social media intern token\"\n    },\n    \"launched_on\": \"2024-01-14T08:15:00Z\",\n    \"risk\": {\n      \"score\": 85,\n      \"risks\": []\n    },\n    \"price_change\": {\n      \"1m\": 2.5,\n      \"5m\": 5.2,\n      \"15m\": 8.7,\n      \"30m\": 12.3,\n      \"1h\": 15.8,\n      \"2h\": 18.2,\n      \"3h\": 20.1,\n      \"4h\": 22.5,\n      \"5h\": 24.8,\n      \"6h\": 26.3,\n      \"12h\": 35.7,\n      \"24h\": 45.2\n    },\n    \"pools\": [\n      {\n        \"pool_id\": \"7pR9vQ2mK8sL4nX6tY1eZ3bW5cA9fD2hG8jM4kN7oP1q\",\n        \"mint\": \"2sHTXKa4orHCk5GoXc6BVueDdrKnXeaWPuNDC2KTpump\",\n        \"market_cap\": 175000,\n        \"liquidity\": 87500.25,\n        \"price\": 0.000175,\n        \"curve_percentage\": 92.5\n      }\n    ]\n  }\n}\n```\n\n**tokens-graduated** - Recently graduated tokens\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: None\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"tokens-graduated\"\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"tokens-graduated\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"symbol\": \"DOLF\",\n    \"name\": \"dolf\",\n    \"mint\": \"3k8GvwEp7Q4B5KzN8vX9ZbX2Kp7YjN9mR5tL8sP6qW4e\",\n    \"decimals\": 6,\n    \"has_metadata_file\": true,\n    \"metadata\": {\n      \"name\": \"dolf\",\n      \"symbol\": \"DOLF\",\n      \"description\": \"Community-driven meme token\"\n    },\n    \"launched_on\": \"2024-01-14T06:20:00Z\",\n    \"risk\": {\n      \"score\": 90,\n      \"risks\": []\n    },\n    \"price_change\": {\n      \"1m\": 5.2,\n      \"5m\": 8.7,\n      \"15m\": 12.3,\n      \"30m\": 18.5,\n      \"1h\": 25.8,\n      \"2h\": 32.1,\n      \"3h\": 38.7,\n      \"4h\": 42.3,\n      \"5h\": 45.8,\n      \"6h\": 48.2,\n      \"12h\": 65.7,\n      \"24h\": 85.4\n    },\n    \"pools\": [\n      {\n        \"pool_id\": \"7pR9vQ2mK8sL4nX6tY1eZ3bW5cA9fD2hG8jM4kN7oP1q\",\n        \"mint\": \"3k8GvwEp7Q4B5KzN8vX9ZbX2Kp7YjN9mR5tL8sP6qW4e\",\n        \"market_cap\": 250000,\n        \"liquidity\": 125000.50,\n        \"price\": 0.00025,\n        \"curve_percentage\": 100.0\n      }\n    ]\n  }\n}\n```\n\n**Parameterized Streams (Require Specific Parameters):**\n\n**pool-changes** - Real-time updates for specific pools\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: pool_id (Pool identifier)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"pool-changes\",\n    \"parameters\": {\n      \"pool_id\": \"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"pool-changes\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"pool_id\": \"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2\",\n    \"mint\": \"DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263\",\n    \"market_cap\": 1250000,\n    \"liquidity\": 198750.25,\n    \"price\": 0.000023,\n    \"decimals\": 6,\n    \"token_supply\": 1000000000,\n    \"liquidity_pool_burned\": 95.5,\n    \"market\": \"pump.fun\",\n    \"quote_token\": \"SOL\",\n    \"bonding_curve\": \"6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P\",\n    \"curve_percentage\": 85.2,\n    \"freeze_authority\": null,\n    \"mint_authority\": null,\n    \"deployer\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\",\n    \"created_at\": \"2024-01-15T10:30:00Z\",\n    \"updated_at\": \"2024-01-15T10:35:00Z\"\n  }\n}\n```\n\n**token-transactions** - Transaction updates for specific tokens\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: token (Token address)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"token-transactions\",\n    \"parameters\": {\n      \"token\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"token-transactions\",\n  \"timestamp\": 1748919450434,\n  \"data\": [\n    {\n      \"transaction_time\": 1748919450,\n      \"wallet\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\",\n      \"program\": \"pump.fun\",\n      \"type\": \"buy\",\n      \"amount\": 1500.25,\n      \"volume_usd\": 34.50,\n      \"volume_sol\": 0.217,\n      \"price\": 0.000023,\n      \"token_in\": {\n        \"symbol\": \"SOL\",\n        \"name\": \"Wrapped SOL\",\n        \"logo\": \"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png\",\n        \"amount\": 0.217,\n        \"price\": 159.25,\n        \"mint\": \"So11111111111111111111111111111111111111112\"\n      },\n      \"token_out\": {\n        \"symbol\": \"TEST\",\n        \"name\": \"Test Token\",\n        \"logo\": \"https://example.com/token-logo.png\",\n        \"amount\": 1500.25,\n        \"price\": 0.000023,\n        \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n      },\n      \"signature\": \"********************************************\"\n    }\n  ]\n}\n```\n\n**price-updates** - Price updates for tokens\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: token (Token address)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"price-updates\",\n    \"parameters\": {\n      \"token\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"price-updates\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"price\": 0.000023,\n    \"volume\": 45000.25,\n    \"marketCap\": 1250000,\n    \"priceChange\": {\n      \"1h\": 2.5,\n      \"24h\": -1.8\n    },\n    \"timestamp\": 1748919450\n  }\n}\n```\n\n**wallet-transactions** - Transaction updates for specific wallets\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: wallet (Wallet address)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"wallet-transactions\",\n    \"parameters\": {\n      \"wallet\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"wallet-transactions\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"transaction_time\": 1748919450,\n    \"wallet\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\",\n    \"program\": \"pump.fun\",\n    \"type\": \"buy\",\n    \"amount\": 3460.25,\n    \"volume_usd\": 79.62,\n    \"volume_sol\": 0.5,\n    \"price\": 0.000023,\n    \"token_in\": {\n      \"symbol\": \"SOL\",\n      \"name\": \"Wrapped SOL\",\n      \"logo\": \"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png\",\n      \"amount\": 0.5,\n      \"price\": 159.25,\n      \"mint\": \"So11111111111111111111111111111111111111112\"\n    },\n    \"token_out\": {\n      \"symbol\": \"TEST\",\n      \"name\": \"Test Token\",\n      \"logo\": \"https://example.com/token-logo.png\",\n      \"amount\": 3460.25,\n      \"price\": 0.000023,\n      \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n    },\n    \"pools\": [\"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2\"],\n    \"signature\": \"5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr\"\n  }\n}\n```\n\n**token-holders** - Token holder count changes (BETA)\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: token (Token address)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"token-holders\",\n    \"parameters\": {\n      \"token\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"token-holders\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\",\n    \"holders\": 1247\n  }\n}\n```\n\n**token-changes** - All updates for specific tokens\n- **Tier Required**: Premium+ (Premium, Enterprise)\n- **Credit Cost**: 0 credits per message\n- **Parameters**: token (Token address)\n\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"token-changes\",\n    \"parameters\": {\n      \"token\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\"\n    }\n  }\n}\n```\n\n**Example Response:**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"token-changes\",\n  \"timestamp\": 1748919450434,\n  \"data\": {\n    \"pool_id\": \"58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2\",\n    \"mint\": \"BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump\",\n    \"market_cap\": 1250000,\n    \"liquidity\": 125000.50,\n    \"price\": 0.000023,\n    \"decimals\": 6,\n    \"token_supply\": 1000000000,\n    \"liquidity_pool_burned\": 95.5,\n    \"market\": \"pump.fun\",\n    \"quote_token\": \"SOL\",\n    \"bonding_curve\": \"6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P\",\n    \"curve_percentage\": 85.2,\n    \"freeze_authority\": null,\n    \"mint_authority\": null,\n    \"deployer\": \"H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6\",\n    \"created_at\": \"2024-01-15T10:30:00Z\",\n    \"updated_at\": \"2024-01-15T10:35:00Z\"\n  }\n}\n```\n\n### Message Protocol\nSubscribe to streams by sending JSON messages:\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"kol-feed\"\n  }\n}\n```\n", "version": "1.0.0", "contact": {"name": "StalkAPI Support", "url": "https://stalkapi.com", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://stalkapi.com/terms"}}, "servers": [{"url": "https://data.stalkapi.com", "description": "Production server"}, {"url": "http://localhost:3000", "description": "Development server"}], "security": [{"ApiKeyAuth": []}], "tags": [{"name": "WebSocket Historical", "description": "Historical data from WebSocket streams"}, {"name": "WebSocket Info", "description": "WebSocket connection management and statistics"}, {"name": "Core Endpoints", "description": "Core system and authentication endpoints"}, {"name": "Smart Money", "description": "Smart money trading insights and analytics"}], "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "API key for authentication. Can be provided as query parameter (?apiKey=YOUR_KEY) or X-API-Key header."}}, "schemas": {"Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error"]}, "User": {"type": "object", "properties": {"id": {"type": "integer", "description": "User ID"}, "email": {"type": "string", "format": "email", "description": "User email"}, "tier_name": {"type": "string", "description": "User tier (free, basic, premium, enterprise)"}, "credits_remaining": {"type": "integer", "description": "Remaining credits"}, "max_websocket_connections": {"type": "integer", "description": "Maximum allowed WebSocket connections"}, "allowed_streams": {"type": "array", "items": {"type": "string"}, "description": "List of streams user has access to"}}}, "KOLFeedItem": {"type": "object", "properties": {"timestamp": {"type": "number", "description": "Unix timestamp when the trading activity occurred", "example": 1748919450434}, "type": {"type": "string", "enum": ["buy", "sell"], "description": "Transaction type (buy or sell)", "example": "buy"}, "kol_label": {"type": "string", "description": "Name/label of the KOL trader", "example": "TraderSZ"}, "wallet": {"type": "string", "description": "Wallet address or 'private' if not public", "example": "********************************************"}, "kol_avatar": {"type": "string", "nullable": true, "description": "URL to KOL's avatar image", "example": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/trader_avatar.jpg"}, "token_in": {"$ref": "#/components/schemas/TokenInfo"}, "token_out": {"$ref": "#/components/schemas/TokenInfo"}, "socials": {"type": "array", "items": {"$ref": "#/components/schemas/SocialProfile"}, "description": "KOL's social media profiles"}, "signature": {"type": "string", "description": "Transaction signature or 'private' if not public", "example": "5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr"}}, "required": ["timestamp", "type", "kol_label", "wallet", "token_in", "token_out", "socials", "signature"]}, "TokenInfo": {"type": "object", "properties": {"symbol": {"type": "string", "nullable": true, "description": "Token symbol", "example": "SOL"}, "name": {"type": "string", "nullable": true, "description": "Token name", "example": "Wrapped SOL"}, "logo": {"type": "string", "nullable": true, "description": "URL to token logo", "example": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"}, "amount": {"type": "number", "nullable": true, "description": "Token amount (numeric)", "example": 0.92}, "amount_string": {"type": "string", "nullable": true, "description": "Token amount (formatted string)", "example": "0.92"}, "amount_usd": {"type": "number", "nullable": true, "description": "USD value of the token amount", "example": 146.47}, "price": {"type": "number", "nullable": true, "description": "Token price in USD", "example": 159.25}, "mint": {"type": "string", "nullable": true, "description": "Token mint address", "example": "So11111111111111111111111111111111111111112"}}}, "SocialProfile": {"type": "object", "properties": {"type": {"type": "string", "description": "Social media platform", "example": "x"}, "handle": {"type": "string", "description": "Social media handle", "example": "trader1sz"}, "followers": {"type": "number", "description": "Number of followers", "example": 658700}}}, "WebSocketInfo": {"type": "object", "properties": {"endpoint": {"type": "string", "description": "WebSocket endpoint URL", "example": "wss://data.stalkapi.com/ws"}, "protocols": {"type": "array", "items": {"type": "string"}, "description": "Supported protocols"}, "authentication": {"type": "object", "properties": {"method": {"type": "string", "description": "Authentication method", "example": "API Key"}, "parameter": {"type": "string", "description": "Parameter name for API key", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "example_url": {"type": "string", "description": "Example connection URL", "example": "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"}}}, "connection_limits": {"type": "object", "properties": {"max_connections": {"type": "integer", "description": "Maximum connections for user's tier"}, "current_tier": {"type": "string", "description": "User's current tier"}}}, "available_streams": {"type": "array", "items": {"type": "string"}, "description": "Streams available to user"}}}, "KafkaStreamItem": {"type": "object", "properties": {"timestamp": {"type": "number", "description": "Unix timestamp when the event occurred"}, "source": {"type": "string", "description": "Source stream name", "enum": ["jupiter-amm-swaps", "pumpfun-amm-swaps", "jupiter-dca-orders"]}, "type": {"type": "string", "description": "Event type", "enum": ["jupiter_amm_swap", "pumpfun_amm_swap", "jupiter_dca_order"]}, "data": {"type": "object", "description": "Raw event data from Kafka stream"}}}, "SolanaTrackerToken": {"type": "object", "properties": {"symbol": {"type": "string", "nullable": true, "description": "Token symbol", "example": "BONK"}, "name": {"type": "string", "nullable": true, "description": "Token name", "example": "Bonk"}, "logo": {"type": "string", "nullable": true, "description": "URL to token logo", "example": "https://arweave.net/hQiPZOsRZXGXBJd_82PhVdlM_hACsT_q6wqwf5cSY7I"}, "mint": {"type": "string", "description": "Token mint address", "example": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"}, "decimals": {"type": "integer", "description": "Token decimals", "example": 5}, "price": {"type": "number", "nullable": true, "description": "Current token price in USD", "example": 2.3e-05}}}, "SolanaTrackerPool": {"type": "object", "properties": {"poolId": {"type": "string", "description": "Pool identifier", "example": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"}, "tokenA": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "tokenB": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "liquidity": {"type": "number", "description": "Pool liquidity in USD", "example": 125000.5}, "volume24h": {"type": "number", "description": "24-hour trading volume in USD", "example": 45000.25}}}, "SolanaTrackerStreamData": {"type": "object", "properties": {"timestamp": {"type": "number", "description": "Unix timestamp when the event occurred", "example": 1748919450434}, "stream": {"type": "string", "description": "Stream name", "enum": ["tokens-launched", "tokens-graduating", "tokens-graduated", "pool-changes", "token-transactions", "price-updates", "wallet-transactions", "token-metadata", "token-holders", "token-changes"]}, "data": {"type": "object", "description": "Stream-specific data payload", "oneOf": [{"$ref": "#/components/schemas/TokenLaunchedData"}, {"$ref": "#/components/schemas/TokenGraduatingData"}, {"$ref": "#/components/schemas/PoolChangesData"}, {"$ref": "#/components/schemas/TransactionData"}, {"$ref": "#/components/schemas/PriceUpdateData"}]}}}, "TokenLaunchedData": {"type": "object", "properties": {"token": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "pools": {"type": "array", "items": {"$ref": "#/components/schemas/SolanaTrackerPool"}, "description": "Associated pools for the token"}, "events": {"type": "array", "items": {"type": "object"}, "description": "Recent events for the token"}, "risk": {"type": "object", "properties": {"lpBurn": {"type": "number", "description": "LP burn percentage", "example": 95.5}, "marketCap": {"type": "number", "description": "Market capitalization in USD", "example": 1250000}, "curvePercentage": {"type": "number", "description": "Bonding curve completion percentage", "example": 85.2}}}}}, "TokenGraduatingData": {"type": "object", "properties": {"token": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "graduationProgress": {"type": "number", "description": "Graduation progress percentage", "example": 92.5}, "threshold": {"type": "number", "nullable": true, "description": "SOL threshold for graduation (if specified)", "example": 175}, "estimatedGraduation": {"type": "string", "nullable": true, "description": "Estimated graduation time", "example": "2024-01-15T10:30:00Z"}}}, "PoolChangesData": {"type": "object", "properties": {"pool": {"$ref": "#/components/schemas/SolanaTrackerPool"}, "changes": {"type": "object", "properties": {"liquidityChange": {"type": "number", "description": "Change in liquidity (USD)", "example": 5000.25}, "priceChange": {"type": "number", "description": "Price change percentage", "example": 2.5}, "volumeChange": {"type": "number", "description": "Volume change (USD)", "example": 1500.75}}}}}, "TokenPriceRequest": {"type": "object", "properties": {"tokenAddress": {"oneOf": [{"type": "string", "description": "Single token address", "example": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}, {"type": "array", "items": {"type": "string"}, "maxItems": 25, "description": "Array of token addresses (max 25)", "example": ["EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump"]}]}}, "required": ["tokenAddress"]}, "TokenPriceData": {"type": "object", "properties": {"tokenAddress": {"type": "string", "description": "Token mint address", "example": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}, "price": {"type": "string", "nullable": true, "description": "Current token price in USD (high precision string)", "example": "0.893596704404597317132775363093366352048947631"}, "market_cap": {"type": "string", "nullable": true, "description": "Market capitalization in USD", "example": "892637331.8339748"}, "24h_volume": {"type": "string", "nullable": true, "description": "24-hour trading volume in USD", "example": "3688406.77435693"}, "24h_price_change_percentage": {"type": "string", "nullable": true, "description": "24-hour price change percentage", "example": "4.0356857922"}, "total_reserve_usd": {"type": "string", "nullable": true, "description": "Total reserve in USD", "example": "8150312.6721030133398176"}}, "required": ["tokenAddress"]}, "TokenPriceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TokenPriceData"}, "description": "Array of token price data"}, "credits_consumed": {"type": "integer", "example": 3}, "message": {"type": "string", "example": "Token price retrieved successfully"}}, "required": ["success", "data", "credits_consumed", "message"]}, "TransactionData": {"type": "object", "properties": {"signature": {"type": "string", "description": "Transaction signature", "example": "5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr"}, "type": {"type": "string", "description": "Transaction type", "enum": ["buy", "sell", "swap", "transfer"], "example": "buy"}, "token": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "amount": {"type": "number", "description": "Transaction amount", "example": 1000000}, "amountUsd": {"type": "number", "description": "Transaction amount in USD", "example": 23.5}, "wallet": {"type": "string", "description": "Wallet address", "example": "********************************************"}}}, "PriceUpdateData": {"type": "object", "properties": {"token": {"$ref": "#/components/schemas/SolanaTrackerToken"}, "price": {"type": "number", "description": "Current price in USD", "example": 2.3e-05}, "priceChange24h": {"type": "number", "description": "24-hour price change percentage", "example": 15.5}, "volume24h": {"type": "number", "description": "24-hour volume in USD", "example": 45000.25}, "pools": {"type": "array", "items": {"$ref": "#/components/schemas/SolanaTrackerPool"}, "description": "Pools providing price data"}}}, "SmartMoneyToken": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time", "description": "Date of the ranking", "example": "2025-06-06T00:00:00.000Z"}, "token_address": {"type": "string", "description": "Token contract address", "example": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"}, "buy_volume": {"type": "number", "description": "Total buy volume", "example": 186862.69698474216}, "sell_volume": {"type": "number", "description": "Total sell volume", "example": 61808.12228895664}, "net_volume": {"type": "number", "description": "Net volume (buy - sell)", "example": 125054.57469578552}, "rank_type": {"type": "string", "enum": ["top", "bottom"], "description": "Ranking type (top for most bought, bottom for most sold)", "example": "top"}, "rank": {"type": "integer", "description": "Token rank (1-10)", "example": 1}, "top_buyer": {"type": "object", "nullable": true, "description": "Top buyer information", "properties": {"wallet": {"type": "string", "description": "Wallet address of top buyer", "example": "GnAHnGRFXrU9cSyYMdvjdUc6BzSj4e6qP6Py79BKZgUB"}, "amount": {"type": "number", "description": "Amount bought by top buyer", "example": 167805.8567037489}}}, "top_seller": {"type": "object", "nullable": true, "description": "Top seller information", "properties": {"wallet": {"type": "string", "description": "Wallet address of top seller", "example": "CubRkB3zzp1ibiiRpKW8vjR7afm5XSSJkg7XKtDX1gBC"}, "amount": {"type": "number", "description": "Amount sold by top seller", "example": 26636.232022063738}}}, "volume_change": {"type": "number", "description": "Volume change percentage", "example": -94.87622732035607}, "rank_change": {"type": "integer", "description": "Rank change from previous period", "example": 0}, "symbol": {"type": "string", "nullable": true, "description": "Token symbol", "example": "Fartcoin"}, "name": {"type": "string", "nullable": true, "description": "Token name", "example": "Fartcoin"}, "decimals": {"type": "integer", "nullable": true, "description": "Token decimals", "example": 6}, "logo": {"type": "string", "nullable": true, "description": "Token logo URL", "example": "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g"}}, "required": ["date", "token_address", "buy_volume", "sell_volume", "net_volume", "rank_type", "rank"]}, "SmartMoneyTimeframeToken": {"type": "object", "properties": {"timeframe": {"type": "string", "description": "Time period for the ranking", "enum": ["24h", "3d", "7d"], "example": "7d"}, "token_address": {"type": "string", "description": "Token contract address", "example": "YXUpFaULqhrLJS79JmFtAsNZQ2JDTnPemmdVZEFpump"}, "buy_volume": {"type": "number", "description": "Total buy volume", "example": 12759.542570171594}, "sell_volume": {"type": "number", "description": "Total sell volume", "example": 66125.2727827169}, "net_volume": {"type": "number", "description": "Net volume (buy - sell)", "example": -53365.730212545306}, "rank_type": {"type": "string", "enum": ["top", "bottom"], "description": "Ranking type (top for best performers, bottom for worst performers)", "example": "bottom"}, "rank": {"type": "integer", "description": "Token rank within timeframe", "example": 25}, "biggest_buyer": {"type": "object", "nullable": true, "description": "Biggest buyer information", "properties": {"wallet": {"type": "string", "description": "Wallet address of biggest buyer", "example": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T"}, "amount": {"type": "number", "description": "Amount bought by biggest buyer", "example": 12759.542570171594}}}, "biggest_seller": {"type": "object", "nullable": true, "description": "Biggest seller information", "properties": {"wallet": {"type": "string", "description": "Wallet address of biggest seller", "example": "GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1"}, "amount": {"type": "number", "description": "Amount sold by biggest seller", "example": 16500.37829474972}}}, "symbol": {"type": "string", "nullable": true, "description": "Token symbol", "example": null}, "name": {"type": "string", "nullable": true, "description": "Token name", "example": null}, "decimals": {"type": "integer", "nullable": true, "description": "Token decimals", "example": null}, "logo": {"type": "string", "nullable": true, "description": "Token logo URL", "example": null}}, "required": ["timeframe", "token_address", "buy_volume", "sell_volume", "net_volume", "rank_type", "rank"]}, "SmartMoneyFlow": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time", "description": "Date of the flow data", "example": "2024-01-15T00:00:00.000Z"}, "buy_volume": {"type": "number", "description": "Total buy volume", "example": 5000000}, "sell_volume": {"type": "number", "description": "Total sell volume", "example": 4500000}, "net_volume": {"type": "number", "description": "Net volume (buy - sell)", "example": 500000}}, "required": ["date", "buy_volume", "sell_volume", "net_volume"]}, "TokenMetadata": {"type": "object", "properties": {"tokenAddress": {"type": "string", "description": "Token address on Solana blockchain", "example": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}, "name": {"type": "string", "description": "Token name", "example": "dogwifhat"}, "symbol": {"type": "string", "description": "Token symbol", "example": "$WIF"}, "decimals": {"type": "integer", "description": "Token decimals", "example": 6}, "logo": {"type": "string", "format": "uri", "description": "Token logo URL", "example": "https://coin-images.coingecko.com/coins/images/33566/large/dogwifhat.jpg?1702499428"}}, "required": ["tokenAddress", "name", "symbol", "decimals", "logo"]}, "TokenMetadataRequest": {"type": "object", "properties": {"tokenAddress": {"oneOf": [{"type": "string", "description": "Single token address", "example": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}, {"type": "array", "items": {"type": "string"}, "maxItems": 30, "description": "Array of token addresses (max 30)", "example": ["EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump"]}]}}, "required": ["tokenAddress"]}, "TokenMetadataResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TokenMetadata"}}, "credits_consumed": {"type": "integer", "example": 3}, "message": {"type": "string", "example": "Token metadata retrieved successfully"}, "cache_info": {"type": "object", "properties": {"note": {"type": "string", "example": "Data served from cache/database when available for optimal performance"}}}}, "required": ["success", "data", "credits_consumed", "message"]}}}, "paths": {"/": {"get": {"tags": ["Core Endpoints"], "summary": "API Root", "description": "Get basic API information and WebSocket endpoint", "security": [], "responses": {"200": {"description": "API information", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "StalkApi"}, "version": {"type": "string", "example": "1.0.0"}, "timestamp": {"type": "string", "format": "date-time"}, "websocket": {"type": "object", "properties": {"endpoint": {"type": "string", "example": "wss://data.stalkapi.com/ws"}, "authentication": {"type": "string", "example": "API key required"}, "example_connection": {"type": "string", "example": "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"}}}}}}}}}}}, "/api/v1/core/token-price": {"post": {"tags": ["Core Endpoints"], "summary": "Get Token Price", "description": "Get current price and market data for Solana tokens max 25 tokens per request (requires Basic tier or higher, consumes 3 credits)", "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenPriceRequest"}, "examples": {"single_token": {"summary": "Single token request", "value": {"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}}, "multiple_tokens": {"summary": "Multiple tokens request", "value": {"tokenAddress": ["EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump"]}}}}}}, "responses": {"200": {"description": "Token price data retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenPriceResponse"}, "examples": {"single_token_success": {"summary": "Single token success", "value": {"success": true, "data": [{"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "price": "0.893596704404597317132775363093366352048947631", "market_cap": "892637331.8339748", "24h_volume": "3688406.77435693", "24h_price_change_percentage": "4.0356857922", "total_reserve_usd": "8150312.6721030133398176"}], "credits_consumed": 3, "message": "Token price retrieved successfully"}}, "multiple_tokens_success": {"summary": "Multiple tokens success", "value": {"success": true, "data": [{"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "price": "0.893596704404597317132775363093366352048947631", "market_cap": "892637331.8339748", "24h_volume": "3688406.77435693", "24h_price_change_percentage": "4.0356857922", "total_reserve_usd": "8150312.6721030133398176"}, {"tokenAddress": "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump", "price": "0.0215240294427430989745915588630198928187561555", "market_cap": "21475407.940906033", "24h_volume": "3505137.70607422", "24h_price_change_percentage": "-17.231500045179928", "total_reserve_usd": "1302442.208296009184474"}], "credits_consumed": 3, "message": "Token price retrieved successfully"}}}}}}, "400": {"description": "Bad request - invalid token address or too many tokens", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/kol-feed/history": {"get": {"tags": ["WebSocket Historical"], "summary": "KOL Feed", "description": "Get historical KOL trading data (requires Basic tier or higher, consumes 3 credits)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "KOL feed history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KOLFeedItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 3}, "message": {"type": "string", "example": "KOL feed history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/jupiter-amm-swaps/history": {"get": {"tags": ["WebSocket Historical"], "summary": "Jupiter AMM Swaps History", "description": "Get historical Jupiter AMM swap data (requires Enterprise tier, 0 credits - included with Enterprise)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Jupiter AMM swaps history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "Jupiter AMM swaps history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/pumpfun-amm-swaps/history": {"get": {"tags": ["WebSocket Historical"], "summary": "Pump.fun AMM Swaps History", "description": "Get historical Pump.fun AMM swap data (requires Enterprise tier, 0 credits - included with Enterprise)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Pump.fun AMM swaps history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "Pump.fun AMM swaps history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/jupiter-dca-orders/history": {"get": {"tags": ["WebSocket Historical"], "summary": "Jupiter DCA Orders History", "description": "Get historical Jupiter DCA order data (requires Enterprise tier, 0 credits - included with Enterprise)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Jupiter DCA orders history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "Jupiter DCA orders history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/core/token-metadata": {"post": {"tags": ["Core Endpoints"], "summary": "Get Token Metadata", "description": "Get token metadata including name, symbol, decimals, and logo. Supports up to 25 token addresses per request. (requires Basic tier or higher, consumes 3 credits)", "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenMetadataRequest"}, "examples": {"single_token": {"summary": "Single token request", "value": {"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"}}, "multiple_tokens": {"summary": "Multiple tokens request", "value": {"tokenAddress": ["EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump"]}}}}}}, "responses": {"200": {"description": "Token metadata retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenMetadataResponse"}, "examples": {"single_token_success": {"summary": "Single token success", "value": {"success": true, "data": [{"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "name": "dogwifhat", "symbol": "$WIF", "decimals": 6, "logo": "https://coin-images.coingecko.com/coins/images/33566/large/dogwifhat.jpg?1702499428"}], "credits_consumed": 3, "message": "Token metadata retrieved successfully", "cache_info": {"note": "Data served from cache/database when available for optimal performance"}}}, "multiple_tokens_success": {"summary": "Multiple tokens success", "value": {"success": true, "data": [{"tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "name": "dogwifhat", "symbol": "$WIF", "decimals": 6, "logo": "https://coin-images.coingecko.com/coins/images/33566/large/dogwifhat.jpg?1702499428"}, {"tokenAddress": "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump", "name": "LABUBU", "symbol": "LABUBU", "decimals": 6, "logo": "https://assets.geckoterminal.com/kmsbuidlgor4fzk6jutnrqwd95ob"}], "credits_consumed": 3, "message": "Token metadata retrieved successfully", "cache_info": {"note": "Data served from cache/database when available for optimal performance"}}}}}}}, "400": {"description": "Bad request - invalid JSON or missing parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Invalid JSON format in request body"}, "code": {"type": "string", "example": "INVALID_JSON"}, "hint": {"type": "string", "example": "Please check your request body format and ensure it contains valid JSON"}, "credits_consumed": {"type": "integer", "example": 0}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Token metadata not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Token metadata not found"}, "credits_consumed": {"type": "integer", "example": 0}, "credit_note": {"type": "string", "example": "No credits charged for server errors"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Failed to retrieve token metadata"}, "credits_consumed": {"type": "integer", "example": 0}, "credit_note": {"type": "string", "example": "No credits charged for server errors"}}}}}}}}}, "/api/v1/smart-money/daily-trends/most-bought-tokens": {"get": {"tags": ["Smart Money"], "summary": "Most Bought Tokens", "description": "Get most bought tokens by smart money traders over the last 15 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Most bought tokens data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Most bought tokens retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/daily-trends/most-sold-tokens": {"get": {"tags": ["Smart Money"], "summary": "Most Sold Tokens", "description": "Get most sold tokens by smart money traders over the last 15 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Most sold tokens data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Most sold tokens retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/daily-trends/daily-flows-sol": {"get": {"tags": ["Smart Money"], "summary": "Daily SOL Flows", "description": "Get daily SOL flow data (buy/sell/net volumes) by smart money traders over the last 15 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Daily SOL flows data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyFlow"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Daily SOL flows retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/daily-trends/daily-flows-meme": {"get": {"tags": ["Smart Money"], "summary": "Daily Meme Flows", "description": "Get daily memecoin flow data (buy/sell/net volumes) by smart money traders over the last 15 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Daily meme flows data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyFlow"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Daily meme flows retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/top-tokens/24h": {"get": {"tags": ["Smart Money"], "summary": "Top Tokens 24h", "description": "Get top performing tokens by smart money traders over the last 24 hours (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Top tokens 24h data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Top tokens 24h retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "24h", "token_address": "YXUpFaULqhrLJS79JmFtAsNZQ2JDTnPemmdVZEFpump", "buy_volume": 12759.542570171594, "sell_volume": 6125.2727827169, "net_volume": 6634.269787454694, "rank_type": "top", "rank": 1, "biggest_buyer": {"wallet": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T", "amount": 12759.542570171594}, "biggest_seller": {"wallet": "GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1", "amount": 6125.2727827169}, "symbol": "PUMP", "name": "Pump Token", "decimals": 6, "logo": "https://example.com/logo.png"}], "credits_consumed": 2, "message": "Top tokens 24h retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/top-tokens/3d": {"get": {"tags": ["Smart Money"], "summary": "Top Tokens 3d", "description": "Get top performing tokens by smart money traders over the last 3 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Top tokens 3d data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Top tokens 3d retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "3d", "token_address": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump", "buy_volume": 25000.123456789, "sell_volume": 18000.987654321, "net_volume": 7000.135802468, "rank_type": "top", "rank": 1, "biggest_buyer": {"wallet": "GnAHnGRFXrU9cSyYMdvjdUc6BzSj4e6qP6Py79BKZgUB", "amount": 25000.123456789}, "biggest_seller": {"wallet": "CubRkB3zzp1ibiiRpKW8vjR7afm5XSSJkg7XKtDX1gBC", "amount": 18000.987654321}, "symbol": "FART", "name": "Fartcoin", "decimals": 6, "logo": "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g"}], "credits_consumed": 2, "message": "Top tokens 3d retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/top-tokens/7d": {"get": {"tags": ["Smart Money"], "summary": "Top Tokens 7d", "description": "Get top performing tokens by smart money traders over the last 7 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Top tokens 7d data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Top tokens 7d retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "7d", "token_address": "So11111111111111111111111111111111111111112", "buy_volume": 150000.789123456, "sell_volume": 95000.456789123, "net_volume": 55000.332334333, "rank_type": "top", "rank": 1, "biggest_buyer": {"wallet": "4STJA1sGVcN3husxLqtoA9oCqSJeQjoLb9WYRjbDvQCD", "amount": 150000.789123456}, "biggest_seller": {"wallet": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T", "amount": 95000.456789123}, "symbol": "SOL", "name": "Wrapped SOL", "decimals": 9, "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"}], "credits_consumed": 2, "message": "Top tokens 7d retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/bottom-tokens/24h": {"get": {"tags": ["Smart Money"], "summary": "Bottom Tokens 24h", "description": "Get worst performing tokens by smart money traders over the last 24 hours (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Bottom tokens 24h data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Bottom tokens 24h retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "24h", "token_address": "ENfpbQUM5xAnNP8ecyEQGFJ6KwbuPjMwv7ZjR29cDuAb", "buy_volume": 1000.123456789, "sell_volume": 15000.987654321, "net_volume": -14000.864197532, "rank_type": "bottom", "rank": 1, "biggest_buyer": {"wallet": "GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1", "amount": 1000.123456789}, "biggest_seller": {"wallet": "4STJA1sGVcN3husxLqtoA9oCqSJeQjoLb9WYRjbDvQCD", "amount": 15000.987654321}, "symbol": "GOONC", "name": "gooncoin", "decimals": 9, "logo": "https://ipfs.io/ipfs/bafkreiebjj52eznuxsmc6djnxz3n6dbuhyt44xv42xqlk3kbaoudog64ji"}], "credits_consumed": 2, "message": "Bottom tokens 24h retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/bottom-tokens/3d": {"get": {"tags": ["Smart Money"], "summary": "Bottom Tokens 3d", "description": "Get worst performing tokens by smart money traders over the last 3 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Bottom tokens 3d data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Bottom tokens 3d retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "3d", "token_address": "TokenAddress3dBottomExample123456789", "buy_volume": 2500.456789123, "sell_volume": 35000.123456789, "net_volume": -32500.666667666, "rank_type": "bottom", "rank": 1, "biggest_buyer": {"wallet": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T", "amount": 2500.456789123}, "biggest_seller": {"wallet": "GnAHnGRFXrU9cSyYMdvjdUc6BzSj4e6qP6Py79BKZgUB", "amount": 35000.123456789}, "symbol": "DUMP", "name": "<PERSON><PERSON>", "decimals": 6, "logo": null}], "credits_consumed": 2, "message": "Bottom tokens 3d retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/smart-money/bottom-tokens/7d": {"get": {"tags": ["Smart Money"], "summary": "Bottom Tokens 7d", "description": "Get worst performing tokens by smart money traders over the last 7 days (requires Basic tier or higher, consumes 2 credits)", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Bottom tokens 7d data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SmartMoneyTimeframeToken"}}, "credits_consumed": {"type": "integer", "example": 2}, "message": {"type": "string", "example": "Bottom tokens 7d retrieved successfully"}}, "example": {"success": true, "data": [{"timeframe": "7d", "token_address": "YXUpFaULqhrLJS79JmFtAsNZQ2JDTnPemmdVZEFpump", "buy_volume": 12759.542570171594, "sell_volume": 66125.2727827169, "net_volume": -53365.730212545306, "rank_type": "bottom", "rank": 25, "biggest_buyer": {"wallet": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T", "amount": 12759.542570171594}, "biggest_seller": {"wallet": "GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1", "amount": 16500.37829474972}, "symbol": null, "name": null, "decimals": null, "logo": null}], "credits_consumed": 2, "message": "Bottom tokens 7d retrieved successfully"}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/info": {"get": {"tags": ["WebSocket Info"], "summary": "WebSocket Connection Info", "description": "Get WebSocket server information and connection details", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "WebSocket connection information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "websocket": {"$ref": "#/components/schemas/WebSocketInfo"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/streams": {"get": {"tags": ["WebSocket Info"], "summary": "Available Streams", "description": "Get list of streams available to the authenticated user", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Available streams", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "streams": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Stream name"}, "description": {"type": "string", "description": "Stream description"}, "tier_required": {"type": "string", "description": "Minimum tier required"}, "credit_cost": {"type": "integer", "description": "Credits consumed per connection"}}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/sessions": {"get": {"tags": ["WebSocket Info"], "summary": "Active Sessions", "description": "Get information about user's active WebSocket sessions", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Active sessions information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "sessions": {"type": "array", "items": {"type": "object", "properties": {"session_id": {"type": "string", "description": "Session identifier"}, "connection_id": {"type": "string", "description": "Connection identifier"}, "connected_at": {"type": "string", "format": "date-time", "description": "Connection timestamp"}, "subscriptions": {"type": "array", "items": {"type": "string"}, "description": "Active stream subscriptions"}, "ip_address": {"type": "string", "description": "Client IP address"}}}}, "total_connections": {"type": "integer", "description": "Total active connections"}, "max_connections": {"type": "integer", "description": "Maximum allowed connections"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/stats": {"get": {"tags": ["WebSocket Info"], "summary": "Usage Statistics", "description": "Get WebSocket usage statistics for the authenticated user", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Usage statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "stats": {"type": "object", "properties": {"total_connections": {"type": "integer", "description": "Total connections made"}, "total_messages": {"type": "integer", "description": "Total messages received"}, "credits_consumed": {"type": "integer", "description": "Total credits consumed via WebSocket"}, "active_streams": {"type": "array", "items": {"type": "string"}, "description": "Currently active streams"}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/credits": {"get": {"tags": ["WebSocket Info"], "summary": "Stream Credit Information", "description": "Get credit costs and information for WebSocket streams", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "stream", "in": "query", "description": "Specific stream to get credit information for", "required": false, "schema": {"type": "string", "example": "kol-feed"}}], "responses": {"200": {"description": "Stream credit information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "credits": {"type": "object", "properties": {"stream_costs": {"type": "object", "description": "Credit costs per stream", "additionalProperties": {"type": "integer"}}, "user_credits": {"type": "integer", "description": "User's remaining credits"}, "billing_period": {"type": "string", "description": "Current billing period"}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}}