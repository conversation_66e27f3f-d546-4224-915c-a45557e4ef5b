/**
 * API Routes Module
 *
 * Main API endpoint definitions for the StalkAPI system providing access to
 * real-time KOL trading data, smart money analytics, token information, and
 * historical data streams. All endpoints implement comprehensive authentication,
 * authorization, rate limiting, and credit management.
 *
 * Features:
 * - Multi-tier access control with endpoint-specific permissions
 * - Credit-based usage tracking and consumption
 * - Rate limiting with Redis-backed storage
 * - Response time monitoring and performance tracking
 * - Comprehensive error handling with credit reversal
 * - Data caching with intelligent TTL management
 * - Input validation and sanitization
 *
 * Endpoint Categories:
 * - Core Services: Token prices, metadata, and fundamental data
 * - Smart Money Analytics: Daily trends and trading patterns
 * - Historical Data: KOL feed, Jupiter swaps, Pump.fun swaps, DCA orders
 * - Real-time Streams: WebSocket data access and management
 * - Testing: Development and debugging endpoints
 *
 * Credit Costs:
 * - Basic endpoints (demo, status): 0-1 credits
 * - Data retrieval (search, analytics): 2-5 credits
 * - Core services (token data): 3 credits
 * - Historical data: 0-3 credits (tier-dependent)
 * - Enterprise features: 0 credits (included in tier)
 *
 * Access Tiers:
 * - Basic: Limited endpoints, basic rate limits
 * - Premium: Extended endpoints, higher rate limits
 * - Enterprise: All endpoints, unlimited rate limits, historical data
 *
 * @module apiRoutes
 * <AUTHOR> Team
 * @version 1.0.0
 */

import express from "express";
import {
  verifyToken,
  checkEndpointAccess,
  checkCredits,
} from "../middleware/auth.js";
import { consumeCredits, responseTime, handleCreditReversal } from "../middleware/credits.js";
import { createUserRateLimit } from "../middleware/rateLimiter.js";
import { cache } from "../config/redis.js";
import { getTokenPrice, getTokenMetadata } from "../../tools/coingecko.js";
import { getTokenPrice as getTokenPriceFromDexscreener, getTokenMetadata as getTokenMetadataFromDexscreener } from "../../tools/dexscreener.js";

const router = express.Router();

/**
 * Response Time Tracking Middleware
 *
 * Applies response time monitoring to all API endpoints for performance
 * analysis and optimization. Tracks request duration and adds timing
 * headers to responses.
 */
router.use(responseTime);

// Public status endpoint (no auth required) - with basic rate limiting
router.get(
  "/status",
  createUserRateLimit(), // Apply rate limiting per endpoint
  async (req, res) => {
    try {
      const status = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || "development",
      };

      res.json(status);
    } catch (error) {
      res.status(500).json({
        error: "Status check failed",
        timestamp: new Date().toISOString(),
      });
    }
  }
);

// Test endpoint for rate limiting - accessible to all tiers including free
router.get(
  "/test/rate-limit",
  verifyToken,
  createUserRateLimit(), // Apply rate limiting after authentication
  async (req, res) => {
    try {
      res.json({
        success: true,
        message: "Rate limit test endpoint",
        user: {
          email: req.user.email,
          tier: req.user.tier_name,
          rate_limit: req.user.max_requests_per_minute,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({
        error: "Test endpoint failed",
      });
    }
  }
);

// KOL Feed History endpoint - requires basic tier or higher
router.get(
  "/kol-feed/history",
  verifyToken,
  createUserRateLimit(), // Apply rate limiting after authentication
  checkEndpointAccess("/api/v1/kol-feed/history"),
  checkCredits(3),
  consumeCredits(3),
  async (req, res) => {
    try {
      const { limit = 100, offset = 0 } = req.query;
      const maxLimit = 100;
      const requestedLimit = Math.min(parseInt(limit), maxLimit);
      const requestedOffset = Math.max(parseInt(offset), 0);

      // Get KOL feed history from Redis
      const historyData = await cache.lrange(
        "kol_feed_history",
        requestedOffset,
        requestedOffset + requestedLimit - 1
      );
      const totalCount = await cache.llen("kol_feed_history");

      res.json({
        success: true,
        data: historyData,
        pagination: {
          limit: requestedLimit,
          offset: requestedOffset,
          total: totalCount,
          returned: historyData.length,
        },
        credits_consumed: 3,
        message: "KOL feed history retrieved successfully",
      });
    } catch (error) {
      console.error("KOL feed history endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve KOL feed history",
      });
    }
  }
);

// Jupiter AMM Swaps History endpoint - requires enterprise tier
router.get(
  "/jupiter-amm-swaps/history",
  verifyToken,
  createUserRateLimit(), // Apply rate limiting after authentication
  checkEndpointAccess("/api/v1/jupiter-amm-swaps/history"),
  checkCredits(0),
  consumeCredits(0),
  async (req, res) => {
    try {
      const { limit = 100, offset = 0 } = req.query;
      const maxLimit = 1000; // Higher limit for enterprise users
      const requestedLimit = Math.min(parseInt(limit), maxLimit);
      const requestedOffset = Math.max(parseInt(offset), 0);

      // Get Jupiter AMM swaps history from Redis
      const historyData = await cache.lrange(
        "jupiter_amm_swaps_history",
        requestedOffset,
        requestedOffset + requestedLimit - 1
      );
      const totalCount = await cache.llen("jupiter_amm_swaps_history");

      res.json({
        success: true,
        data: historyData,
        pagination: {
          limit: requestedLimit,
          offset: requestedOffset,
          total: totalCount,
          returned: historyData.length,
        },
        credits_consumed: 0,
        message: "Jupiter AMM swaps history retrieved successfully",
      });
    } catch (error) {
      console.error("Jupiter AMM swaps history endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve Jupiter AMM swaps history",
      });
    }
  }
);

// Pump.fun AMM Swaps History endpoint - requires enterprise tier
router.get(
  "/pumpfun-amm-swaps/history",
  verifyToken,
  createUserRateLimit(), // Apply rate limiting after authentication
  checkEndpointAccess("/api/v1/pumpfun-amm-swaps/history"),
  checkCredits(0),
  consumeCredits(0),
  async (req, res) => {
    try {
      const { limit = 100, offset = 0 } = req.query;
      const maxLimit = 1000; // Higher limit for enterprise users
      const requestedLimit = Math.min(parseInt(limit), maxLimit);
      const requestedOffset = Math.max(parseInt(offset), 0);

      // Get Pump.fun AMM swaps history from Redis
      const historyData = await cache.lrange(
        "pumpfun_amm_swaps_history",
        requestedOffset,
        requestedOffset + requestedLimit - 1
      );
      const totalCount = await cache.llen("pumpfun_amm_swaps_history");

      res.json({
        success: true,
        data: historyData,
        pagination: {
          limit: requestedLimit,
          offset: requestedOffset,
          total: totalCount,
          returned: historyData.length,
        },
        credits_consumed: 0,
        message: "Pump.fun AMM swaps history retrieved successfully",
      });
    } catch (error) {
      console.error("Pump.fun AMM swaps history endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve Pump.fun AMM swaps history",
      });
    }
  }
);

// Jupiter DCA Orders History endpoint - requires enterprise tier
router.get(
  "/jupiter-dca-orders/history",
  verifyToken,
  createUserRateLimit(), // Apply rate limiting after authentication
  checkEndpointAccess("/api/v1/jupiter-dca-orders/history"),
  checkCredits(0),
  consumeCredits(0),
  async (req, res) => {
    try {
      const { limit = 100, offset = 0 } = req.query;
      const maxLimit = 1000; // Higher limit for enterprise users
      const requestedLimit = Math.min(parseInt(limit), maxLimit);
      const requestedOffset = Math.max(parseInt(offset), 0);

      // Get Jupiter DCA orders history from Redis
      const historyData = await cache.lrange(
        "jupiter_dca_orders_history",
        requestedOffset,
        requestedOffset + requestedLimit - 1
      );
      const totalCount = await cache.llen("jupiter_dca_orders_history");

      res.json({
        success: true,
        data: historyData,
        pagination: {
          limit: requestedLimit,
          offset: requestedOffset,
          total: totalCount,
          returned: historyData.length,
        },
        credits_consumed: 0,
        message: "Jupiter DCA orders history retrieved successfully",
      });
    } catch (error) {
      console.error("Jupiter DCA orders history endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve Jupiter DCA orders history",
      });
    }
  }
);

// Smart Money - Daily Trends - Most Bought Tokens
router.get(
  "/smart-money/daily-trends/most-bought-tokens",
  verifyToken,
  createUserRateLimit(), // Re-enabled with memory store (Redis store was causing hanging)
  checkEndpointAccess("/api/v1/smart-money/daily-trends/most-bought-tokens"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get most bought tokens from Redis
      const mostBoughtTokens = await cache.get(
        "smart_money:daily_trends:most_bought_tokens"
      );

      if (!mostBoughtTokens) {
        return res.status(404).json({
          success: false,
          error: "Smart money data not available",
          // Credits will be automatically set to 0 by middleware for 4xx errors
        });
      }

      res.json({
        success: true,
        data: mostBoughtTokens,
        message: "Most bought tokens retrieved successfully",
        // credits_consumed will be automatically added by middleware
      });
    } catch (error) {
      console.error("Smart Money - Most Bought Tokens endpoint error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to retrieve most bought tokens",
        // Credits will be automatically set to 0 by middleware for 5xx errors
      });
    }
  }
);

// Smart Money - Daily Trends - Most Sold Tokens
router.get(
  "/smart-money/daily-trends/most-sold-tokens",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/daily-trends/most-sold-tokens"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get most sold tokens from Redis
      const mostSoldTokens = await cache.get(
        "smart_money:daily_trends:most_sold_tokens"
      );
      res.json({
        success: true,
        data: mostSoldTokens,
        credits_consumed: 2,
        message: "Most sold tokens retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Most Sold Tokens endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve most sold tokens",
      });
    }
  }
);

// Smart Money - Daily Trends - Daily Flows SOL
router.get(
  "/smart-money/daily-trends/daily-flows-sol",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/daily-trends/daily-flows-sol"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily SOL flows from Redis
      const dailyFlowsSol = await cache.get(
        "smart_money:daily_trends:daily_flows_sol"
      );
      res.json({
        success: true,
        data: dailyFlowsSol,
        credits_consumed: 2,
        message: "Daily SOL flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows SOL endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily SOL flows",
      });
    }
  }
);

// Smart Money - Daily Trends - Daily Flows Meme
router.get(
  "/smart-money/daily-trends/daily-flows-meme",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/daily-trends/daily-flows-meme"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const dailyFlowsMeme = await cache.get(
        "smart_money:daily_trends:daily_flows_meme"
      );
      res.json({
        success: true,
        data: dailyFlowsMeme,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Top Tokens 24h
router.get(
  "/smart-money/top-tokens/24h",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/top-tokens/24h"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const topTokens24h = await cache.get(
        "smart_money:top_tokens:24h"
      );
      res.json({
        success: true,
        data: topTokens24h,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Top Tokens 3d
router.get(
  "/smart-money/top-tokens/3d",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/top-tokens/3d"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const topTokens3d = await cache.get(
        "smart_money:top_tokens:3d"
      );
      res.json({
        success: true,
        data: topTokens3d,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Top Tokens 7d
router.get(
  "/smart-money/top-tokens/7d",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/top-tokens/7d"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const topTokens7d = await cache.get(
        "smart_money:top_tokens:3d"
      );
      res.json({
        success: true,
        data: topTokens7d,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Bottom Tokens 24h
router.get(
  "/smart-money/bottom-tokens/24h",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/bottom-tokens/24h"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const bottomTokens24h = await cache.get(
        "smart_money:bottom_tokens:24h"
      );
      res.json({
        success: true,
        data: bottomTokens24h,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Bottom Tokens 3d
router.get(
  "/smart-money/bottom-tokens/3d",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/bottom-tokens/3d"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const bottomTokens3d = await cache.get(
        "smart_money:bottom_tokens:3d"
      );
      res.json({
        success: true,
        data: bottomTokens3d,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

// Smart Money - Bottom Tokens 7d
router.get(
  "/smart-money/bottom-tokens/7d",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/smart-money/bottom-tokens/7d"),
  checkCredits(2),
  consumeCredits(2),
  async (req, res) => {
    try {
      // Get daily meme flows from Redis
      const bottomTokens7d = await cache.get(
        "smart_money:bottom_tokens:7d"
      );
      res.json({
        success: true,
        data: bottomTokens7d,
        credits_consumed: 2,
        message: "Daily meme flows retrieved successfully",
      });
    } catch (error) {
      console.error("Smart Money - Daily Flows Meme endpoint error:", error);
      res.status(500).json({
        error: "Failed to retrieve daily meme flows",
      });
    }
  }
);

/**
 * Token Address Validation Middleware
 *
 * Validates Solana token addresses for CoinGecko API endpoints.
 * Supports both single token addresses (string) and batch requests (array).
 * Ensures proper format and enforces batch size limits for performance.
 *
 * @middleware
 * @param {Object} req - Express request object with tokenAddress in body
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void}
 *
 * Validation Rules:
 * - Token address must be present in request body
 * - Can be a string (single token) or array (batch request)
 * - Array must not be empty and cannot exceed 25 tokens
 * - Follows Solana token mint address format
 *
 * @example
 * // Single token request
 * { "tokenAddress": "So11111111111111111111111111111111111111112" }
 *
 * // Batch token request
 * { "tokenAddress": ["So11111111111111111111111111111111111111112", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"] }
 */
function validateCoinGeckoTokenAddress(req, res, next) {
  const { tokenAddress } = req.body;

  if (!tokenAddress) {
    return res.status(400).json({
      error: "Invalid or missing 'tokenAddress' in request body",
    });
  }

  // Validate array requests
  if(Array.isArray(tokenAddress) && tokenAddress.length === 0) {
    return res.status(400).json({
      error: "Token address array is empty",
    });
  }

  // Enforce batch size limit for performance
  if(Array.isArray(tokenAddress) && tokenAddress.length > 25) {
    return res.status(400).json({
      error: "Too many tokens to fetch (max 25)",
    });
  }

  // Validate data type
  if(!Array.isArray(tokenAddress) && typeof tokenAddress !== "string") {
    return res.status(400).json({
      error: "Invalid token address format",
    });
  }

  next();
}
router.post(
  "/core/token-price",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/core/token-price"),
  validateCoinGeckoTokenAddress,
  checkCredits(3),
  consumeCredits(3),
  async (req, res) => {
    try {
      const { tokenAddress } = req.body;
      // getTokenPriceFromDexscreener
      // const tokenPrice = await getTokenPrice(tokenAddress);
      const tokenPrice = await Promise.race([
        getTokenPrice(tokenAddress),
        getTokenPriceFromDexscreener(tokenAddress),
      ]);

      if (!tokenPrice) {
        return res.status(404).json({
          success: false,
          error: "Token price not found",
          // Credits will be automatically set to 0 by middleware for 4xx errors
        });
      }

      res.json({
        success: true,
        data: tokenPrice,
        message: "Token price retrieved successfully",
        // credits_consumed will be automatically added by middleware
      });
    } catch (error) {
      console.error("Core - Token Price endpoint error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to retrieve token price",
        // Credits will be automatically set to 0 by middleware for 5xx errors
      });
    }
  }
);

router.post(
  "/core/token-metadata",
  verifyToken,
  createUserRateLimit(),
  checkEndpointAccess("/api/v1/core/token-metadata"),
  validateCoinGeckoTokenAddress,
  checkCredits(3),
  consumeCredits(3),
  async (req, res) => {
    try {
      const { tokenAddress } = req.body;

      // Get token metadata with caching (Redis -> DB -> API)
      // const tokenMetadata = await getTokenMetadata(tokenAddress);
      const tokenMetadata = await Promise.race([
        getTokenMetadata(tokenAddress),
        getTokenMetadataFromDexscreener(tokenAddress),
      ]);

      if (!tokenMetadata) {
        return res.status(404).json({
          success: false,
          error: "Token metadata not found",
          // Credits will be automatically set to 0 by middleware for 4xx errors
        });
      }

      // Return response immediately (cache updates happen in background)
      res.json({
        success: true,
        data: tokenMetadata,
        message: "Token metadata retrieved successfully",
        // cache_info: {
        //   note: "Data served from cache/database when available for optimal performance"
        // }
        // credits_consumed will be automatically added by middleware
      });

    } catch (error) {
      console.error("Core - Token Metadata endpoint error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to retrieve token metadata",
        // Credits will be automatically set to 0 by middleware for 5xx errors
      });
    }
  }
);

// Development test endpoints (remove in production)
if (process.env.NODE_ENV === 'development') {
    // Simple test endpoint without middleware
    router.get('/test-simple', async (req, res) => {
        try {
            res.json({
                success: true,
                message: 'Simple test endpoint working',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Simple test endpoint error:', error);
            res.status(500).json({
                error: 'Simple test failed'
            });
        }
    });

    // Test endpoint with auth + rate limit
    router.get('/test-auth-rate',
        verifyToken,
        createUserRateLimit(),
        async (req, res) => {
            try {
                res.json({
                    success: true,
                    message: 'Redis rate limiter test successful',
                    user: {
                        email: req.user.email,
                        tier: req.user.tier_name
                    },
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error('Rate limiter test endpoint error:', error);
                res.status(500).json({
                    error: 'Rate limiter test failed'
                });
            }
        }
    );

    // Test Smart Money data directly from Redis (no middleware)
    router.get('/test-smart-money-direct', async (req, res) => {
        try {
            const mostBoughtTokens = await cache.get('smart_money:daily_trends:most_bought_tokens');

            if (!mostBoughtTokens) {
                return res.json({
                    success: false,
                    message: 'No data found in Redis cache',
                    cache_key: 'smart_money:daily_trends:most_bought_tokens'
                });
            }

            res.json({
                success: true,
                data: mostBoughtTokens.slice(0, 3), // Return first 3 items for testing
                total_items: mostBoughtTokens.length,
                message: 'Smart Money data test successful'
            });
        } catch (error) {
            res.status(500).json({
                error: 'Smart Money direct test failed',
                details: error.message
            });
        }
    });
}

export default router;
