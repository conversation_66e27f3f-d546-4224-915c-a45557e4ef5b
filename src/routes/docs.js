/**
 * Documentation Routes Module
 *
 * Provides comprehensive API documentation interface and OpenAPI specification
 * serving for the StalkAPI system. Delivers interactive documentation, API
 * reference materials, and developer resources through modern documentation tools.
 *
 * Features:
 * - Interactive API documentation with Scalar API Reference
 * - OpenAPI 3.0 specification serving and validation
 * - Real-time API testing and exploration interface
 * - Comprehensive endpoint documentation with examples
 * - Authentication flow documentation and guides
 * - Rate limiting and credit system explanations
 * - WebSocket stream documentation and examples
 * - Developer guides and integration tutorials
 *
 * Documentation Tools:
 * - Scalar API Reference for modern, interactive docs
 * - OpenAPI 3.0 specification for standardized API description
 * - Real-time API testing capabilities
 * - Code examples in multiple programming languages
 * - Authentication and authorization flow documentation
 *
 * Content Organization:
 * - API endpoint reference with detailed parameters
 * - Authentication and security documentation
 * - Rate limiting and credit system guides
 * - WebSocket streaming documentation
 * - Error handling and troubleshooting guides
 * - SDK and integration examples
 *
 * Developer Experience:
 * - Interactive API testing interface
 * - Copy-paste ready code examples
 * - Comprehensive error code documentation
 * - Performance optimization guides
 * - Best practices and usage patterns
 *
 * @module docsRoutes
 * <AUTHOR> Team
 * @version 1.0.0
 */

import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { getScalarConfig, getScalarHTML } from '../docs/scalar-config.js';
import { loadOpenAPISpec } from '../docs/openapi-loader.js';

// ES module compatibility for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// Serve Scalar static assets
router.use('/assets', express.static(path.join(__dirname, '../../node_modules/@scalar/api-reference/dist')));

// Serve Scalar API documentation
router.get('/', (req, res) => {
    try {
        // Get the base URL for the API
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        
        // Get Scalar configuration
        const config = getScalarConfig(baseUrl);
        
        // Generate HTML with Scalar
        const html = getScalarHTML(config);

        res.setHeader('Content-Type', 'text/html');
        res.send(html);

    } catch (error) {
        console.error('Error serving documentation:', error);
        res.status(500).json({
            error: 'Failed to load documentation',
            message: error.message
        });
    }
});

// Serve the OpenAPI spec as JSON
router.get('/openapi.json', (req, res) => {
    try {
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        const spec = loadOpenAPISpec(baseUrl);

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.json(spec);

    } catch (error) {
        console.error('Error serving OpenAPI spec:', error);
        res.status(500).json({
            error: 'Failed to load OpenAPI specification',
            message: error.message
        });
    }
});

// API documentation info endpoint
router.get('/info', (req, res) => {
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    
    res.json({
        title: 'StalkAPI Documentation',
        description: 'Real-time KOL trading data and analytics API documentation',
        version: '1.0.0',
        documentation_url: `${baseUrl}/docs`,
        openapi_spec: {
            json: `${baseUrl}/docs/openapi.json`
        },
        powered_by: 'Scalar API Reference',
        last_updated: new Date().toISOString()
    });
});

export default router;
