/**
 * Authentication Routes Module
 *
 * Provides user authentication and account management endpoints for the
 * StalkAPI system. Handles user profile access, usage statistics, and
 * account information retrieval with comprehensive security measures.
 *
 * Features:
 * - User profile and account information access
 * - Usage statistics and analytics with configurable time periods
 * - Credit status and consumption tracking
 * - Rate limiting for security protection
 * - API key-based authentication (no JWT tokens)
 * - Comprehensive error handling and logging
 *
 * Security Considerations:
 * - Registration and login handled by frontend website for security
 * - No password-based authentication in API (API key only)
 * - Rate limiting on all endpoints to prevent abuse
 * - User data sanitization before response
 * - Comprehensive audit logging for account access
 *
 * Endpoints:
 * - GET /auth/profile - User profile and account information
 * - GET /auth/usage - Usage statistics and analytics
 * - GET /auth/credits - Credit balance and consumption details
 *
 * @module authRoutes
 * <AUTHOR> Team
 * @version 1.0.0
 */

import express from 'express';
import { User } from '../models/User.js';
import { verifyToken } from '../middleware/auth.js';
import { createEndpointRateLimit } from '../middleware/rateLimiter.js';
import { getCreditStatus } from '../middleware/credits.js';

const router = express.Router();

/**
 * Authentication Endpoint Rate Limiting
 *
 * Applies strict rate limiting to authentication endpoints to prevent
 * abuse and protect against brute force attacks. Configured for moderate
 * usage patterns while maintaining security.
 */
const authRateLimit = createEndpointRateLimit(10, 15); // 10 requests per 15 minutes

// Note: Registration and login are handled by the frontend website
// These endpoints have been removed for security reasons

// Note: Refresh token endpoint removed - API now uses only API key authentication



/**
 * User Profile Endpoint
 *
 * Retrieves comprehensive user profile information including account details,
 * tier information, and current status. Sanitizes sensitive information
 * before returning to client.
 *
 * @route GET /auth/profile
 * @middleware verifyToken - Validates API key and loads user information
 * @middleware authRateLimit - Applies rate limiting for security
 * @returns {Object} User profile with account and tier information
 *
 * Response includes:
 * - Basic account information (email, creation date, etc.)
 * - Current tier details and permissions
 * - Credit balance and usage information
 * - Account status and settings
 */
router.get('/profile', verifyToken, authRateLimit, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);

        if (!user) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        res.json({
            success: true,
            profile: user.toJSON(), // Automatically excludes sensitive data
            tier: {
                name: user.tier_name,
                max_credits_per_month: user.max_credits_per_month,
                max_requests_per_minute: user.max_requests_per_minute,
                max_websocket_connections: user.max_websocket_connections,
                allowed_endpoints: user.allowed_endpoints,
                allowed_streams: user.allowed_streams
            }
        });

    } catch (error) {
        console.error('Profile fetch error:', error);
        res.status(500).json({
            error: 'Failed to fetch user profile'
        });
    }
});

/**
 * Usage Statistics Endpoint
 *
 * Retrieves detailed usage statistics and analytics for the authenticated user.
 * Supports configurable time periods for historical analysis and trend tracking.
 *
 * @route GET /auth/usage
 * @middleware verifyToken - Validates API key and loads user information
 * @query {number} days - Number of days to include in statistics (default: 30)
 * @returns {Object} Usage statistics and analytics data
 *
 * Statistics include:
 * - Total requests and credit consumption
 * - Average response times and performance metrics
 * - Unique endpoints accessed and usage patterns
 * - Daily breakdown of activity
 * - Trend analysis and insights
 */
router.get('/usage', verifyToken, async (req, res) => {
    try {
        const { days = 30 } = req.query;
        const user = await User.findById(req.user.id);

        if (!user) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        const stats = await user.getUsageStats(parseInt(days));

        res.json({
            success: true,
            usage: stats,
            period: `${days} days`,
            summary: {
                total_requests: stats.reduce((sum, day) => sum + parseInt(day.total_requests || 0), 0),
                total_credits: stats.reduce((sum, day) => sum + parseInt(day.total_credits_consumed || 0), 0),
                unique_endpoints: new Set(stats.map(day => day.unique_endpoints || 0)).size,
                period_days: parseInt(days)
            }
        });

    } catch (error) {
        console.error('Usage stats error:', error);
        res.status(500).json({
            error: 'Failed to fetch usage statistics'
        });
    }
});

/**
 * Credit Status Endpoint
 *
 * Retrieves current credit balance, consumption details, and billing information
 * for the authenticated user. Provides real-time credit status for usage tracking.
 *
 * @route GET /auth/credits
 * @middleware verifyToken - Validates API key and loads user information
 * @returns {Object} Credit balance and consumption information
 *
 * Response includes:
 * - Current credit balance and monthly allowance
 * - Credits used this month and remaining
 * - Billing cycle information
 * - Usage rate and projected consumption
 */
router.get('/credits', verifyToken, async (req, res) => {
    try {
        const creditStatus = await getCreditStatus(req.user.id);

        res.json({
            success: true,
            credits: creditStatus,
            tier_info: {
                name: req.user.tier_name,
                monthly_allowance: req.user.max_credits_per_month,
                unlimited: req.user.max_credits_per_month === -1
            }
        });

    } catch (error) {
        console.error('Credit status error:', error);
        res.status(500).json({
            error: 'Failed to fetch credit status'
        });
    }
});

export default router;
