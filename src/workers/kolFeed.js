/**
 * KOL Feed Worker Module
 *
 * Real-time WebSocket worker that connects to external KOL (Key Opinion Leader)
 * trading data feed and processes trading activity from verified KOL traders.
 * Transforms raw data to standardized format and publishes to internal Redis
 * channels for real-time distribution to WebSocket clients.
 *
 * Features:
 * - Real-time WebSocket connection to external KOL data provider
 * - Data transformation from external format to internal API format
 * - Redis pub/sub integration for real-time message distribution
 * - Historical data storage with configurable retention (last 100 messages)
 * - Automatic reconnection handling with exponential backoff
 * - Comprehensive error handling and logging
 *
 * Data Flow:
 * External KOL WebSocket → KolFeed Worker → Redis Pub/Sub → StreamManager → WebSocket Clients
 *                                      ↓
 *                                 Redis List Storage → REST API History Endpoint
 *
 * Environment Variables Required:
 * - STALKCHAIN_CENTRAL_WSS_URL: WebSocket URL for KOL data feed
 * - STALKCHAIN_CENTRAL_KEY: API key for authentication
 *
 * @module kolFeed
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { pubsub, cache } from "../config/redis.js";
import { WebSocket } from "ws";

// Configuration constants for KOL feed WebSocket connection
const KOL_FEED_CHANNEL = "kol_feed_internal";     // Redis channel for internal pub/sub
const KOL_FEED_HISTORY_KEY = "kol_feed_history";  // Redis key for historical data storage
const WS_SOURCES = "kol";                         // Source filter for KOL data
const WS_URL = `${process.env.STALKCHAIN_CENTRAL_WSS_URL}/feed?apiKey=${process.env.STALKCHAIN_CENTRAL_KEY}&sources=${WS_SOURCES}`;

/**
 * Token Mint Address Extraction Utility
 *
 * Extracts token mint address from token objects, handling both correct
 * and misspelled field names from the external API for backward compatibility.
 *
 * @param {Object} token - Token object from KOL feed data
 * @returns {string|null} Token mint address or null if not found
 *
 * @example
 * const mintAddress = getTokenMint({ tokenAddress: "So11111..." });
 * // Returns: "So11111..."
 */
function getTokenMint(token) {
  return token?.tokenAddress || token?.tokenAdress || null; // Handle API typo
}

/**
 * KOL Feed Data Transformation Function
 *
 * Transforms raw KOL feed data from external API format to standardized
 * internal format using snake_case naming conventions. Handles privacy
 * settings and ensures consistent data structure for downstream processing.
 *
 * @param {Object} json - Raw KOL feed message from WebSocket
 * @returns {Object} Transformed data in standardized format
 * @throws {Error} If required transaction data is missing
 *
 * @example
 * const transformed = transformKolFeedData({
 *   timestamp: 1640995200000,
 *   tx: { transactionType: "swap", wallet_label: "KOL Trader", ... }
 * });
 */
function transformKolFeedData(json) {
  if (!json?.tx) throw new Error("Missing tx in KOL feed message");

  const tx = json.tx;

  return {
    timestamp: json.timestamp,                                    // Unix timestamp
    type: tx.transactionType,                                     // Transaction type (swap, etc.)
    kol_label: tx.wallet_label,                                   // KOL trader display name
    wallet: tx.isPublic ? tx.walletAddress : "private",          // Wallet address (privacy-aware)
    kol_avatar: tx.wallet_avatar,                                 // KOL avatar image URL

    // Input token information
    token_in: {
      symbol: tx.tokenIn?.symbol,                                 // Token symbol (e.g., "SOL")
      name: tx.tokenIn?.name,                                     // Full token name
      logo: tx.tokenIn?.logo,                                     // Token logo URL
      amount: tx.tokenIn?.amount,                                 // Token amount (numeric)
      amount_string: tx.tokenIn?.tokenAmountString,               // Formatted amount string
      amount_usd: tx.tokenIn?.tokenInAmountUsd,                   // USD value
      price: tx.tokenIn?.price,
      mint: getTokenMint(tx.tokenIn),
    },
    token_out: {
      symbol: tx.tokenOut?.symbol,
      name: tx.tokenOut?.name,
      logo: tx.tokenOut?.logo,
      amount: tx.tokenOut?.amount,
      amount_string: tx.tokenOut?.tokenAmountString,
      amount_usd: tx.tokenOut?.tokenOutAmountUsd,
      price: tx.tokenOut?.price,
      mint: getTokenMint(tx.tokenOut),
    },
    socials: tx.socials,
    signature: tx.isPublic ? tx.tx : "private",
  };
}

/**
 * KOL Feed Worker Class
 *
 * Manages WebSocket connection to external KOL feed service and processes
 * real-time trading activity from Key Opinion Leaders (KOLs). Handles
 * connection lifecycle, data transformation, and message distribution.
 *
 * Responsibilities:
 * - Establish and maintain WebSocket connection to external KOL data provider
 * - Transform incoming data to standardized internal format
 * - Publish processed data to Redis pub/sub for real-time distribution
 * - Store historical data in Redis list for REST API access
 * - Handle connection errors and implement reconnection logic
 * - Provide graceful shutdown capabilities
 *
 * Connection Management:
 * - Automatic reconnection on connection loss
 * - Exponential backoff for failed connections
 * - Heartbeat monitoring for connection health
 * - Graceful shutdown with proper cleanup
 */
export class KolFeed {
  /**
   * Initialize KOL Feed worker instance
   *
   * Sets up initial state for WebSocket connection management.
   * Worker starts in stopped state and must be explicitly initialized.
   */
  constructor() {
    this.isRunning = false;    // Worker operational state flag
    this.ws = null;            // WebSocket connection instance
  }

  /**
   * Initialize KOL feed worker and establish WebSocket connection
   * Validates required environment variables before starting
   * @throws {Error} If required environment variables are missing
   */
  async init() {
    if (
      !process.env.STALKCHAIN_CENTRAL_WSS_URL ||
      !process.env.STALKCHAIN_CENTRAL_KEY
    ) {
      throw new Error(
        "Missing required environment variables for KolFeed WebSocket"
      );
    }
    this.isRunning = true;
    this.connect();
  }

  /**
   * Stop the KOL feed worker and close WebSocket connection
   */
  stop() {
    this.isRunning = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * Establish WebSocket connection to KOL feed service
   * Sets up event handlers for message processing, error handling, and reconnection
   */
  connect() {
    this.ws = new WebSocket(WS_URL);
    this.ws.on("open", () => {
      console.log("[KOLFeed] Connected to WebSocket");
    });

    this.ws.on("message", (message) => {
      try {
        const json = JSON.parse(message);

        // Process KOL feed messages
        if (json?.tx && json?.source === WS_SOURCES && json?.tx?.isKol) {
          // Check if worker is still running before processing
          if (!this.isRunning) {
            return;
          }

          const transformedData = transformKolFeedData(json);

          // Publish to pub/sub for real-time subscribers
          pubsub
            .publish(KOL_FEED_CHANNEL, transformedData)
            .catch((error) => {
              if (this.isRunning) {
                console.error("❌ [KOLFeed] Failed to publish message:", error);
              }
            });

          // Store in Redis list for history (keep last 100 messages)
          cache
            .lpushAndTrim(KOL_FEED_HISTORY_KEY, transformedData, 100)
            .then((success) => {
              if (!success && this.isRunning) {
                console.error("❌ [KOLFeed] Failed to store message in history");
              }
            })
            .catch((error) => {
              if (this.isRunning) {
                console.error("❌ [KOLFeed] Error storing message in history:", error);
              }
            });
        }
      } catch (error) {
        console.error("❌ [KOLFeed] Error processing message:", error);
      }
    });

    this.ws.on("error", (error) => {
      console.error("[KOLFeed] WebSocket error:", error);
      this.stop();
      // Optionally add exponential backoff here for reconnects
      if (this.isRunning) {
        setTimeout(() => this.connect(), 2000);
      }
    });

    this.ws.on("close", () => {
      console.log("[KOLFeed] WebSocket connection closed");
      if (this.isRunning) {
        setTimeout(() => this.connect(), 2000); // Add reconnect delay
      }
    });
  }
}

/* 
{
  type: 'txSwap',
  source: 'kol',
  tx: {
    tokenIn: {
      symbol: 'SOL',
      name: 'Wrapped SOL',
      logo: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
      tokenAmountString: '0.92',
      amount: 0.9197152919999994,
      tokenInAmountUsd: 146.465609,
      price: 159.251032,
      tokenAdress: 'So11111111111111111111111111111111111111112',
      tokenAddress: 'So11111111111111111111111111111111111111112'
    },
    tokenOut: {
      symbol: 'CAREBUBU',
      name: 'NEW CAREBEAR LABUBU DOLLS',
      logo: '',
      tokenAmountString: '16.71M',
      amount: 16708878.061636,
      tokenOutAmountUsd: 145.000953,
      price: 0.000009,
      tokenAdress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump',
      tokenAddress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump'
    },
    timestamp: 1748913012,
    chain: 'solana',
    tx: '5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr',
    walletAddress: '********************************************',
    wallet_label: 'Jidn',
    wallet_avatar: 'https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/jidn_w.jpg',
    isPublic: true,
    isKol: true,
    socials: [ [Object], [Object] ],
    totalUsd: 145,
    totalUsdNumber: 145.000953,
    transactionType: 'buy'
  },
  timestamp: 1748913955272
}
*/
