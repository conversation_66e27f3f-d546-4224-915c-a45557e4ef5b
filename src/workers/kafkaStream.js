/**
 * Kafka Streams Worker Module
 *
 * High-performance Kafka consumer worker that processes real-time stream data
 * from Jupiter AMM swaps, Pump.fun swaps, and DCA orders. Transforms raw
 * blockchain data into standardized format and distributes via Redis pub/sub.
 *
 * Features:
 * - Multi-topic Kafka consumer with automatic partition assignment
 * - Real-time data transformation and standardization
 * - Redis pub/sub integration for message distribution
 * - Historical data caching with configurable retention
 * - SSL/TLS secure connections to Kafka brokers
 * - Comprehensive error handling and retry mechanisms
 * - Connection pooling and resource management
 * - Graceful shutdown with proper cleanup
 *
 * Supported Data Streams:
 * - jupiter-amm-swaps: Jupiter DEX automated market maker swap transactions
 * - pumpfun-amm-swaps: Pump.fun platform swap transactions
 * - jupiter-dca-orders: Jupiter Dollar Cost Averaging order executions
 *
 * Data Flow:
 * Kafka Topics → KafkaStreams Worker → Redis Pub/Sub → StreamManager → WebSocket Clients
 *                                   ↓
 *                              Redis List Storage → REST API History Endpoints
 *
 * Environment Variables Required:
 * - KAFKA_CLIENT_ID: Unique identifier for this Kafka client
 * - KAFKA_BROKERS: Comma-separated list of Kafka broker addresses
 * - KAFKA_CERT: SSL certificate authority for secure connections
 * - KAFKA_USER_KEY: SSL client private key for authentication
 * - KAFKA_USER_CERT: SSL client certificate for authentication
 *
 * @module kafkaStream
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { pubsub, cache } from "../config/redis.js";
import { Kafka } from "kafkajs";
import { convertKeysToSnakeCase } from "../utils/caseConverter.js";

// Configuration constants
const HISTORY_RETENTION_COUNT = 1000; // Store last 1000 messages per stream for historical access

/**
 * Kafka SSL Configuration
 *
 * Secure connection configuration for Kafka brokers using SSL/TLS.
 * Ensures encrypted communication and client authentication.
 */
const KAFKA_SSL = {
  ca: [process.env.KAFKA_CERT],        // Certificate Authority for server verification
  key: [process.env.KAFKA_USER_KEY],   // Client private key for authentication
  cert: [process.env.KAFKA_USER_CERT], // Client certificate for authentication
  rejectUnauthorized: true,            // Enforce SSL certificate validation
};

/**
 * Stream Configuration Mapping
 *
 * Defines the mapping between internal stream names and their storage
 * configuration, including Redis keys and display names for each data stream.
 */
const STREAM_CONFIG = {
  "jupiter-amm-swaps": {
    historyKey: "jupiter_amm_swaps_history",       // Redis list key for historical data
    displayName: "Jupiter AMM Swaps"               // Human-readable stream name
  },
  "pumpfun-amm-swaps": {
    historyKey: "pumpfun_amm_swaps_history",       // Redis list key for historical data
    displayName: "Pump.fun AMM Swaps"              // Human-readable stream name
  },
  "jupiter-dca-orders": {
    historyKey: "jupiter_dca_orders_history",      // Redis list key for historical data
    displayName: "Jupiter DCA Orders"              // Human-readable stream name
  }
};

/**
 * Kafka Stream Data Transformation Function
 *
 * Transforms raw Kafka stream data from external format to standardized
 * internal format using snake_case naming conventions. Adds topic-specific
 * metadata and ensures consistent data structure across all stream types.
 *
 * @param {string} topic - Kafka topic name (internal stream identifier)
 * @param {Object} data - Raw message data from Kafka topic
 * @returns {Object} Transformed data in standardized format with metadata
 *
 * @example
 * const transformed = transformKafkaStreamData("jupiter-amm-swaps", {
 *   tokenIn: { symbol: "SOL" },
 *   tokenOut: { symbol: "USDC" },
 *   amountIn: 1000000000
 * });
 * // Returns: { token_in: { symbol: "SOL" }, token_out: { symbol: "USDC" }, ... }
 */
function transformKafkaStreamData(topic, data) {
  // Convert all camelCase keys to snake_case recursively for API consistency
  const snakeCaseData = convertKeysToSnakeCase(data);

  // Base transformation applied to all stream types
  const baseTransform = {
    timestamp: Date.now(),    // Processing timestamp for ordering
    source: topic,            // Source stream identifier
    ...snakeCaseData         // Transformed data with snake_case keys
  };

  // Add topic-specific transformations and type identifiers
  switch (topic) {
    case "jupiter-amm-swaps":
      return {
        ...baseTransform,
        type: "jupiter_amm_swap"    // Standardized type identifier
      };
    case "pumpfun-amm-swaps":
      return {
        ...baseTransform,
        type: "pumpfun_amm_swap"    // Standardized type identifier
      };
    case "jupiter-dca-orders":
      return {
        ...baseTransform,
        type: "jupiter_dca_order"   // Standardized type identifier
      };
    default:
      return baseTransform;         // Fallback for unknown topics
  }
}

/**
 * Kafka Streams Worker Class
 *
 * Manages Kafka consumer connections and processes real-time stream data
 * from Jupiter AMM swaps, Pump.fun swaps, and DCA orders.
 * Publishes processed data to Redis pub/sub and maintains historical cache.
 */
export class KafkaStreams {
  constructor() {
    this.isRunning = false; // Worker state flag
    this.kafka = new Kafka({
      clientId: process.env.KAFKA_CLIENT_ID,
      brokers: [process.env.KAFKA_BROKERS],
      ssl: KAFKA_SSL,
      connectionTimeout: 30000,
      authenticationTimeout: 30000,
      retry: {
        initialRetryTime: 100,
        retries: 100,
        maxRetryTime: 30000,
        factor: 2,
      },
    });
  }

  /**
   * Initialize Kafka streams worker
   * Validates environment variables and starts connection
   * @throws {Error} If required environment variables are missing
   */
  async init() {
    if (!process.env.KAFKA_CLIENT_ID || !process.env.KAFKA_BROKERS) {
      throw new Error("Missing required Kafka environment variables");
    }
    this.connect();
  }

  /**
   * Stop the Kafka streams worker and disconnect consumer
   */
  stop() {
    this.isRunning = false;
    if (this.consumer) {
      this.consumer.disconnect();
      this.consumer = null;
    }
  }

  async connect() {
    if (this.isRunning) return;
    this.isRunning = true;

    try {
      this.consumer = this.kafka.consumer({
        groupId: `stalkapi-consumer-${Date.now()}`,
      });

      console.log("[KAFKA] Connecting to Kafka...");
      await this.consumer.connect();
      console.log("[KAFKA] Connected to Kafka successfully");

      // Subscribe to the different streams
      console.log("[KAFKA] Subscribing to topics...");
      await Promise.all([
        this.consumer.subscribe({
          topic: "jupiter-amm-swaps",
          fromBeginning: false, // Only process new messages
        }),
        this.consumer.subscribe({
          topic: "pumpfun-amm-swaps",
          fromBeginning: false, // Only process new messages
        }),
        this.consumer.subscribe({
          topic: "stalkchain-dca-swaps",
          fromBeginning: false, // Only process new messages
        }),
      ]);
      console.log("[KAFKA] Successfully subscribed to all topics");
    } catch (error) {
      // console.error("[KAFKA] Error during connection:", error);
      this.isRunning = false;
      // Retry connection after delay
      if (this.isRunning !== false) { // Only retry if not explicitly stopped
        setTimeout(() => this.connect(), 5000);
      }
      return;
    }

    await this.consumer.run({
      eachMessage: async ({ topic, message }) => {
        try {
          // Check if worker is still running before processing
          if (!this.isRunning) {
            return;
          }

          const rawData = JSON.parse(message?.value?.toString());

          const topicMapping = {
            "jupiter-amm-swaps": "jupiter-amm-swaps",
            "pumpfun-amm-swaps": "pumpfun-amm-swaps",
            "stalkchain-dca-swaps": "jupiter-dca-orders",
          };

          const streamName = topicMapping[topic];
          if (!streamName) {
            console.warn(`⚠️ [KAFKA] Unknown topic: ${topic}`);
            return;
          }

          // Transform the data
          const transformedData = transformKafkaStreamData(streamName, rawData);

          // Publish to pub/sub for real-time subscribers
          await pubsub.publish("kafka_streams_internal", {
            stream: streamName,
            data: transformedData,
            timestamp: Date.now(),
          });

          // Store in Redis history (keep last 1000 messages per stream)
          const config = STREAM_CONFIG[streamName];
          if (config) {
            await cache.lpushAndTrim(config.historyKey, transformedData, HISTORY_RETENTION_COUNT);
          }

        } catch (error) {
          if (this.isRunning) {
            console.error(`❌ [KAFKA] Error processing message from topic ${topic}:`, error);
          }
        }
      },
    });
  }
}
