import { pubsub } from "../config/redis.js";
import { subscribeCentral } from "../config/redisCentral.js";

export class StalkchainCentral {
  constructor() {
    this.isRunning = false;
  }

  async init() {
    this.isRunning = true;
    this.connect();
  }

  connect() {
    subscribeCentral("fresh_wallet_feed", (message) => {
      try {
        // Check if worker is still running before processing
        if (!this.isRunning) {
          return;
        }

        // Publish the data to the internal Redis channel for StreamManager
        pubsub.publish("stalkchain_central_internal", {
          stream: "fresh-wallet-feed",
          data: this.formatData(message),
          timestamp: Date.now(),
        });

        // console.log(
        //   `✅ [StalkchainCentral] Published fresh wallet feed data:`,
        //   {
        //     type: message.type,
        //     wallet: message.data?.wallet,
        //     timestamp: new Date().toISOString(),
        //   }
        // );
      } catch (error) {
        if (this.isRunning) {
          console.error(
            "❌ [StalkchainCentral] Error processing message:",
            error
          );
        }
      }
    });

    console.log(
      "✅ [StalkchainCentral] Connected to fresh_wallet_feed channel"
    );
  }

  stop() {
    this.isRunning = false;
    console.log("🛑 [StalkchainCentral] Worker stopped");
  }

  formatData(data) {
    // TODO: Add any necessary data formatting here
    return {
      platform: data.data?.source,
      type: data.data?.type,
      timestamp: new Date(data.data?.trxDate),
      wallet: data.data?.wallet,
      wallet_age: new Date(data.data?.walletAge),
      wallet_first_activity: new Date(data.data?.walletAgeDetailed?.firstActivity),
      wallet_transactions_count: data.data?.walletAgeDetailed?.txCount,
      wallet_funding_mint: data.data?.walletFundingMint,
      wallet_funding_amount: data.data?.walletFunding,
      wallet_funding_source: data.data?.walletAgeDetailed?.source?.type,
      wallet_funding_source_wallet: data.data?.walletAgeDetailed?.source?.wallet,
      wallet_funding_source_name: data.data?.walletAgeDetailed?.source?.name || "Unknown",
      sol_amount: data.data?.solAmount,
      usd_amount: data.data?.usdAmount,
      token_in: {
        mint: data.data?.inputMint,
        symbol: data.data?.inputMintMetaData?.ticker,
        name: data.data?.inputMintMetaData?.name,
        decimals: data.data?.inputDecimals,
        logo: data.data?.inputMintMetaData?.icon,
        amount: Number(data.data?.inputAmount) / Math.pow(10, data.data?.inputDecimals),
        amount_string: (Number(data.data?.inputAmount) / Math.pow(10, data.data?.inputDecimals)).toString(),
        amount_usd: data.data?.usdAmount,
        price: data.data?.inputTokenPrice
          ? Number(data.data?.inputTokenPrice)
          : data.data?.usdAmount / Number(data.data?.inputAmount),
      },
      token_out: {
        mint: data.data?.outputMint,
        symbol: data.data?.outputMintMetaData?.ticker,
        name: data.data?.outputMintMetaData?.name,
        decimals: data.data?.outputDecimals,
        logo: data.data?.outputMintMetaData?.icon,
        amount: Number(data.data?.outputAmount) / Math.pow(10, data.data?.outputDecimals),
        amount_string: (Number(data.data?.outputAmount) / Math.pow(10, data.data?.outputDecimals)).toString(),
        amount_usd: data.data?.usdAmount,
        price: data.data?.outputTokenPrice
          ? Number(data.data?.outputTokenPrice)
          : data.data?.usdAmount / Number(data.data?.outputAmount),
      },
      signature: data.data?.signature,
      // debug: data
    };
  }
}