/**
 * Solana Tracker Worker Module
 *
 * Advanced WebSocket worker that manages connections to the SolanaTracker API
 * for real-time Solana blockchain data streaming. Implements sophisticated
 * connection pooling, room-based subscriptions, and automatic cleanup mechanisms.
 *
 * Features:
 * - Connection pooling with per-room WebSocket management
 * - Automatic reconnection with exponential backoff and jitter
 * - Room-based subscription management for different data streams
 * - Bulk unsubscription and cleanup capabilities
 * - Transaction deduplication to prevent duplicate processing
 * - Comprehensive error handling and logging
 * - Memory leak prevention with automatic connection cleanup
 *
 * Supported Streams:
 * - tokens-graduating: Tokens graduating from bonding curves
 * - tokens-launched: Newly launched tokens
 * - tokens-latest: Latest token activity
 * - tokens-top-volume: Tokens with highest trading volume
 * - tokens-top-gainers: Tokens with highest price gains
 * - tokens-top-losers: Tokens with highest price losses
 *
 * Architecture:
 * - ConnectionPool: Manages individual WebSocket connections per room
 * - SolanaTracker: Main worker class coordinating all operations
 * - EventEmitter: Provides event-driven architecture for connection management
 *
 * Environment Variables Required:
 * - SOLANA_TRACKER_WSS_URL: WebSocket URL for SolanaTracker API
 * - SOLANA_TRACKER_WSS_KEY: API key for authentication
 *
 * @module solanaTracker
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { pubsub } from "../config/redis.js";
import { WebSocket } from "ws";
import EventEmitter from "events";

// Configuration constants
const SOLANA_TRACKER_CHANNEL = "solana_tracker_internal";  // Redis channel for internal pub/sub
const RECONNECT_DELAY_BASE = 2500;                         // Base reconnection delay (ms)
const RECONNECT_DELAY_MAX = 4500;                          // Maximum reconnection delay (ms)
const RANDOMIZATION_FACTOR = 0.5;                          // Jitter factor for reconnection timing

// Stream configuration mapping
const STREAM_CONFIG = {
  latest: {
    displayName: "Latest Tokens/Pools",
    requiresParams: false,
  },
  graduating: {
    displayName: "Graduating Tokens",
    requiresParams: false,
    supportsParams: true,
    paramFormat: "graduating:sol:{market_cap}",
  },
  graduated: {
    displayName: "Graduated Tokens",
    requiresParams: false,
  },
  pool: {
    displayName: "Pool Changes",
    requiresParams: true,
  },
  transaction: {
    displayName: "Transactions",
    requiresParams: true,
  },
  price: {
    displayName: "Price Updates",
    requiresParams: true,
  },
  wallet: {
    displayName: "Wallet Transactions",
    requiresParams: true,
  },
  holders: {
    displayName: "Token Holders",
    requiresParams: true,
  },
  token: {
    displayName: "Token Changes",
    requiresParams: true,
  },
};

/**
 * Data transformation functions for different SolanaTracker room types
 */

// [DONE] Transform latest tokens/pools data
function transformLatestData(data) {
  if (!data?.token) {
    throw new Error("Missing token data in latest message");
  }

  return {
    symbol: data.token.symbol,
    name: data.token.name,
    mint: data.token.mint,
    decimals: data.token.decimals,
    has_metadata_file: data.token.hasFileMetaData || false,
    metadata: data.token.uri || {},
    launched_on: data.token.createdOn || null,
    risk: data?.risk
      ? {
          score: data?.risk?.score || 0,
          risks: data?.risk?.risks
            ? data?.risk?.risks.map((risk) => ({
                name: risk.name,
                description: risk.description,
                level: risk.level,
                score: risk.score,
              }))
            : [],
        }
      : {},
    pools: innerTransformPoolData(data?.pools || []),
  };
}

// [DONE] Transform transaction data
function transformTransactionData(data) {
  return data.map((tx) => ({
    transaction_time: tx.time,
    wallet: tx.wallet,
    program: tx.program,
    type: tx.type,
    amount: tx.amount,
    volume_usd: tx.volume,
    volume_sol: tx.solVolume,
    price: tx.priceUsd,
    token_in: {
      symbol: tx.token.from?.symbol,
      name: tx.token.from?.name,
      logo: tx.token.from?.image,
      amount: tx.token.from?.amount,
      price: tx.token.from?.price?.usd,
      mint: tx.token.from?.address,
    },
    token_out: {
      symbol: tx.token.to?.symbol,
      name: tx.token.to?.name,
      logo: tx.token.to?.image,
      amount: tx.token.to?.amount,
      price: tx.token.to?.price?.usd,
      mint: tx.token.to?.address,
    },
    signature: tx.tx || tx.signature,
  }));
}

// [DONE] Transform price update data
function transformPriceData(data) {
  return data;
}

// [DONE] Transform pool change data
function transformPoolData(data) {
  return innerTransformPoolData([data])[0];
}

// [DONE] Transform wallet transaction data
function transformWalletData(data) {
  return {
    transaction_time: data.time,
    wallet: data.wallet,
    program: data.program,
    type: data.type,
    amount: data.type === " buy" ? data.to.amount : data.from.amount,
    volume_usd: data.volume.usd,
    volume_sol: data.volume.sol,
    price: data.price.usd,
    token_in: {
      symbol: data.from.token?.symbol,
      name: data.from.token?.name,
      logo: data.from.token?.image,
      amount: data.from.token?.amount,
      price: data.from.token?.price?.usd,
      mint: data.from.token?.address,
    },
    token_out: {
      symbol: data.to.token?.symbol,
      name: data.to.token?.name,
      logo: data.to.token?.image,
      amount: data.to.token?.amount,
      price: data.to.token?.price?.usd,
      mint: data.to.token?.address,
    },
    pools: data.pools,
    signature: data.tx
  }
}

// [DONE] Transform graduating/graduated token data
function transformGraduatingData(data) {
  if (!data?.token) {
    throw new Error("Missing token data in latest message");
  }

  return {
    symbol: data.token.symbol,
    name: data.token.name,
    mint: data.token.mint,
    decimals: data.token.decimals,
    has_metadata_file: data.token.hasFileMetaData || false,
    metadata: data.token.uri || {},
    launched_on: data.token.createdOn || null,
    risk: data?.risk
      ? {
          score: data?.risk?.score || 0,
          risks: data?.risk?.risks
            ? data?.risk?.risks.map((risk) => ({
                name: risk.name,
                description: risk.description,
                level: risk.level,
                score: risk.score,
              }))
            : [],
        }
      : {},
    price_change: {
      "1m": data?.events?.["1m"]?.priceChangePercentage || 0,
      "5m": data?.events?.["5m"]?.priceChangePercentage || 0,
      "15m": data?.events?.["15m"]?.priceChangePercentage || 0,
      "30m": data?.events?.["30m"]?.priceChangePercentage || 0,
      "1h": data?.events?.["1h"]?.priceChangePercentage || 0,
      "2h": data?.events?.["2h"]?.priceChangePercentage || 0,
      "3h": data?.events?.["3h"]?.priceChangePercentage || 0,
      "4h": data?.events?.["4h"]?.priceChangePercentage || 0,
      "5h": data?.events?.["5h"]?.priceChangePercentage || 0,
      "6h": data?.events?.["6h"]?.priceChangePercentage || 0,
      "12h": data?.events?.["12h"]?.priceChangePercentage || 0,
      "24h": data?.events?.["24h"]?.priceChangePercentage || 0,
    },
    pools: innerTransformPoolData(data?.pools || []),
  };
}



// [DONE] Transform holders update data
function transformHoldersData(data, room) {
  return {
    mint: room.split(":")[1],
    holders: data.total
  };
}

// [DONE] Transform token change data
function transformTokenData(data) {
  return innerTransformPoolData([data])[0];
}

// Main transformation dispatcher
function transformSolanaTrackerData(room, data) {
  try {
    const roomType = room.split(":")[0];

    switch (roomType) {
      case "latest":
        return transformLatestData(data);
      case "transaction":
        return transformTransactionData(data);
      case "price":
        return transformPriceData(data);
      case "pool":
        return transformPoolData(data);
      case "wallet":
        return transformWalletData(data);
      case "graduating":
        return transformGraduatingData(data, "graduating", room);
      case "graduated":
        return transformGraduatingData(data, "graduated", room);
      case "holders":
        return transformHoldersData(data, room);
      case "token":
        return transformTokenData(data);
      default:
        console.warn(`⚠️ [SolanaTracker] Unknown room type: ${roomType}`);
        return {
          type: roomType,
          timestamp: Date.now(),
          raw: data,
        };
    }
  } catch (error) {
    console.error(
      `❌ [SolanaTracker] Error transforming data for room ${room}:`,
      error
    );
    return {
      type: "error",
      timestamp: Date.now(),
      room: room,
      error: error.message,
      raw: data,
    };
  }
}

// custom pool data universal transform
function innerTransformPoolData(data)  {
  return data?.map(pool => {
    return {
      pool_id: pool.poolId,
      mint: pool.tokenAddress,
      market_cap: pool.marketCap,
      liquidity: pool.liquidity,
      price: pool.price,
      decimals: pool.decimals,
      token_supply: pool.tokenSupply,
      liquidity_pool_burned: pool.lpBurn,
      market: pool.market,
      quote_token: pool.quoteToken,
      decimals: pool.decimals,
      bonding_curve: pool.curve,
      curve_percentage: pool.curvePercentage,
      freeze_authority: pool.security?.freezeAuthority,
      mint_authority: pool.security?.mintAuthority,
      deployer: pool.deployer,
      created_at: pool.createdAt,
      updated_at: pool.updatedAt,
    }
  });
}

/**
 * Connection Pool Manager for SolanaTracker WebSocket connections
 */
class ConnectionPool {
  constructor() {
    this.connections = new Map(); // room -> connection info
    this.subscribers = new Map(); // room -> Set of subscriber IDs
  }

  // Get or create connection for a room
  getConnection(room) {
    return this.connections.get(room);
  }

  // Add subscriber to a room
  addSubscriber(room, subscriberId) {
    if (!this.subscribers.has(room)) {
      this.subscribers.set(room, new Set());
    }
    this.subscribers.get(room).add(subscriberId);

    console.log(
      `✅ [SolanaTracker] Added subscriber ${subscriberId} to room ${room}. Total: ${
        this.subscribers.get(room).size
      }`
    );
  }

  // Remove subscriber from a room
  removeSubscriber(room, subscriberId) {
    if (!this.subscribers.has(room)) {
      return false;
    }

    const roomSubscribers = this.subscribers.get(room);
    roomSubscribers.delete(subscriberId);

    console.log(
      `🗑️ [SolanaTracker] Removed subscriber ${subscriberId} from room ${room}. Remaining: ${roomSubscribers.size}`
    );

    // If no more subscribers, mark for cleanup
    if (roomSubscribers.size === 0) {
      this.subscribers.delete(room);
      return true; // Indicates room should be cleaned up
    }

    return false;
  }

  // Get subscriber count for a room
  getSubscriberCount(room) {
    return this.subscribers.get(room)?.size || 0;
  }

  // Set connection for a room
  setConnection(room, connection) {
    this.connections.set(room, connection);
  }

  // Remove connection for a room
  removeConnection(room) {
    const connection = this.connections.get(room);
    if (connection && connection.ws) {
      try {
        // Remove all event listeners to prevent memory leaks
        connection.ws.removeAllListeners();

        // Close the connection if it's still open
        if (connection.ws.readyState === connection.ws.OPEN ||
            connection.ws.readyState === connection.ws.CONNECTING) {
          connection.ws.close();
        }
      } catch (error) {
        console.error(`❌ [SolanaTracker] Error closing connection for room ${room}:`, error);
        // Force terminate if close fails
        try {
          connection.ws.terminate();
        } catch (termError) {
          console.error(`❌ [SolanaTracker] Error terminating connection for room ${room}:`, termError);
        }
      }
    }
    this.connections.delete(room);
    console.log(`🗑️ [SolanaTracker] Removed connection for room ${room}`);
  }

  // Get all active rooms
  getActiveRooms() {
    return Array.from(this.connections.keys());
  }

  // Clean up empty connections
  cleanup() {
    for (const room of this.connections.keys()) {
      if (this.getSubscriberCount(room) === 0) {
        this.removeConnection(room);
      }
    }
  }
}

/**
 * SolanaTracker WebSocket Worker with Connection Pooling
 */
export class SolanaTracker {
  constructor() {
    this.isRunning = false;
    this.wsUrl = null;
    this.connectionPool = new ConnectionPool();
    this.reconnectAttempts = new Map(); // room -> attempt count
    this.emitter = new EventEmitter();
    this.transactions = new Set(); // Deduplication set for transactions
  }

  async init() {
    // Validate environment variables
    if (
      !process.env.SOLANA_TRACKER_WSS_URL ||
      !process.env.SOLANA_TRACKER_WSS_KEY
    ) {
      throw new Error(
        "Missing required environment variables: SOLANA_TRACKER_WSS_URL and SOLANA_TRACKER_WSS_KEY"
      );
    }

    // Correct URL format: WSS_URL/API_KEY
    this.wsUrl = `${process.env.SOLANA_TRACKER_WSS_URL}/${process.env.SOLANA_TRACKER_WSS_KEY}`;
    this.isRunning = true;

    console.log("✅ [SolanaTracker] Initialized successfully");
    console.log(
      `🔗 [SolanaTracker] WebSocket URL: ${process.env.SOLANA_TRACKER_WSS_URL}`
    );
  }

  stop() {
    this.isRunning = false;

    // Close all connections
    for (const room of this.connectionPool.getActiveRooms()) {
      this.connectionPool.removeConnection(room);
    }

    // Clear state
    this.reconnectAttempts.clear();
    this.transactions.clear();

    console.log("🛑 [SolanaTracker] Stopped all connections");
  }

  // Subscribe to a SolanaTracker room
  async subscribe(room, subscriberId = "default") {
    if (!this.isRunning) {
      throw new Error("SolanaTracker worker is not running");
    }

    // Add subscriber to the room
    this.connectionPool.addSubscriber(room, subscriberId);

    // Check if connection already exists for this room
    let connection = this.connectionPool.getConnection(room);

    if (!connection) {
      // Create new connection for this room
      connection = await this.createConnection(room);
      if (!connection) {
        this.connectionPool.removeSubscriber(room, subscriberId);
        throw new Error(`Failed to create connection for room: ${room}`);
      }
    }

    return {
      room,
      subscriberId,
      subscriberCount: this.connectionPool.getSubscriberCount(room),
    };
  }

  // Unsubscribe from a SolanaTracker room
  async unsubscribe(room, subscriberId = "default") {
    console.log(`🔄 [SolanaTracker] Unsubscribing ${subscriberId} from room ${room}`);

    const shouldCleanup = this.connectionPool.removeSubscriber(
      room,
      subscriberId
    );

    if (shouldCleanup) {
      // No more subscribers, close the connection
      const connection = this.connectionPool.getConnection(room);
      if (connection && connection.ws) {
        console.log(`🧹 [SolanaTracker] Cleaning up connection for room ${room} - no more subscribers`);

        // Send leave message if connection is still open
        if (connection.ws.readyState === WebSocket.OPEN) {
          this.leaveRoom(connection.ws, room);
        }

        // Close connection immediately and clean up
        this.connectionPool.removeConnection(room);

        // Clear any pending reconnection attempts for this room
        this.reconnectAttempts.delete(room);

        console.log(`✅ [SolanaTracker] Successfully cleaned up room ${room}`);
      }
    }

    return {
      room,
      subscriberId,
      subscriberCount: this.connectionPool.getSubscriberCount(room),
    };
  }

  // Create a new WebSocket connection for a room
  async createConnection(room) {
    try {
      console.log(`🔌 [SolanaTracker] Creating connection for room: ${room}`);

      const ws = new WebSocket(this.wsUrl);
      const connection = {
        ws,
        room,
        isConnected: false,
        lastActivity: Date.now(),
      };

      // Set up WebSocket event handlers
      await this.setupWebSocketHandlers(ws, room, connection);

      // Store connection
      this.connectionPool.setConnection(room, connection);

      return connection;
    } catch (error) {
      console.error(
        `❌ [SolanaTracker] Failed to create connection for room ${room}:`,
        error
      );
      return null;
    }
  }

  // Setup WebSocket event handlers
  async setupWebSocketHandlers(ws, room, connection) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Connection timeout for room: ${room}`));
      }, 10000);

      ws.on("open", () => {
        clearTimeout(timeout);
        console.log(`✅ [SolanaTracker] Connected to room: ${room}`);

        connection.isConnected = true;
        this.reconnectAttempts.delete(room);

        // Join the room
        this.joinRoom(ws, room);
        resolve();
      });

      ws.on("message", (message) => {
        try {
          connection.lastActivity = Date.now();
          const data = JSON.parse(message.toString());

          // Handle ping/pong messages
          if (data.type === "ping") {
            ws.send(JSON.stringify({ type: "pong" }));
            return;
          }

          // Handle join confirmations
          if (data.type === "joined") {
            console.log(
              `✅ [SolanaTracker] Successfully joined room: ${
                data.room || room
              }`
            );
            return;
          }

          // Handle actual data messages
          if (data.type === "message") {
            this.handleMessage(room, data);
          } else {
            // Log other message types for debugging
            if (process.env.NODE_ENV === "development") {
              console.log(
                `🔍 [SolanaTracker] Unknown message type for room ${room}:`,
                data.type
              );
            }
          }
        } catch (error) {
          console.error(
            `❌ [SolanaTracker] Error processing message for room ${room}:`,
            error
          );
        }
      });

      ws.on("error", (error) => {
        clearTimeout(timeout);
        console.error(
          `❌ [SolanaTracker] WebSocket error for room ${room}:`,
          error
        );
        connection.isConnected = false;

        if (
          this.isRunning &&
          this.connectionPool.getSubscriberCount(room) > 0
        ) {
          this.scheduleReconnect(room);
        }

        reject(error);
      });

      ws.on("close", () => {
        console.log(`🔌 [SolanaTracker] Connection closed for room: ${room}`);
        connection.isConnected = false;

        if (
          this.isRunning &&
          this.connectionPool.getSubscriberCount(room) > 0
        ) {
          this.scheduleReconnect(room);
        }
      });
    });
  }

  // Handle incoming messages
  async handleMessage(room, message) {
    try {
      if (!message.data) {
        return;
      }

      // Check if worker is still running before processing
      if (!this.isRunning) {
        return;
      }

      // Log raw data in development for debugging (only for specific debugging)
      if (process.env.NODE_ENV === "development" && process.env.DEBUG_SOLANA_TRACKER === "true") {
        console.log(
          `📨 [SolanaTracker] Raw message for room ${room}:`,
          JSON.stringify(message, null, 2)
        );
      }

      // Deduplicate transactions based on token mint or transaction signature
      const dedupeKey =
        message.data.token?.mint || message.data.tx || message.data.signature;
      if (dedupeKey && this.transactions.has(dedupeKey)) {
        return;
      } else if (dedupeKey) {
        this.transactions.add(dedupeKey);

        // Clean up old transactions (keep last 10000)
        if (this.transactions.size > 10000) {
          const transactionsArray = Array.from(this.transactions);
          this.transactions.clear();
          transactionsArray
            .slice(-5000)
            .forEach((tx) => this.transactions.add(tx));
        }
      }

      // Transform the data
      const transformedData = transformSolanaTrackerData(room, message.data);

      // Publish to internal Redis channel
      await pubsub.publish(SOLANA_TRACKER_CHANNEL, {
        room,
        data: transformedData,
        timestamp: Date.now(),
      });

      console.log(
        `✅ [SolanaTracker] Processed message for room ${room} - Token: ${
          message.data.token?.symbol || "N/A"
        }`
      );
    } catch (error) {
      if (this.isRunning) {
        console.error(
          `❌ [SolanaTracker] Error handling message for room ${room}:`,
          error
        );
        console.error(
          `❌ [SolanaTracker] Message data:`,
          JSON.stringify(message, null, 2)
        );
      }
    }
  }

  // Join a room
  joinRoom(ws, room) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: "join", room }));
      console.log(`📥 [SolanaTracker] Joined room: ${room}`);
    }
  }

  // Leave a room
  leaveRoom(ws, room) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: "leave", room }));
      console.log(`📤 [SolanaTracker] Left room: ${room}`);
    }
  }

  // Schedule reconnection with exponential backoff
  scheduleReconnect(room) {
    const attempts = this.reconnectAttempts.get(room) || 0;
    this.reconnectAttempts.set(room, attempts + 1);

    const delay = Math.min(
      RECONNECT_DELAY_BASE * Math.pow(2, attempts),
      RECONNECT_DELAY_MAX
    );
    const jitter = delay * RANDOMIZATION_FACTOR;
    const reconnectDelay = delay + Math.random() * jitter;

    console.log(
      `🔄 [SolanaTracker] Scheduling reconnect for room ${room} in ${Math.round(
        reconnectDelay
      )}ms (attempt ${attempts + 1})`
    );

    setTimeout(async () => {
      if (this.isRunning && this.connectionPool.getSubscriberCount(room) > 0) {
        console.log(
          `🔄 [SolanaTracker] Attempting reconnect for room: ${room}`
        );

        // Remove old connection
        this.connectionPool.removeConnection(room);

        // Create new connection
        const connection = await this.createConnection(room);
        if (!connection) {
          // If reconnection fails, schedule another attempt
          this.scheduleReconnect(room);
        }
      }
    }, reconnectDelay);
  }

  // Get connection statistics
  getStats() {
    const activeRooms = this.connectionPool.getActiveRooms();
    const stats = {
      isRunning: this.isRunning,
      activeConnections: activeRooms.length,
      totalSubscribers: 0,
      rooms: {},
    };

    for (const room of activeRooms) {
      const subscriberCount = this.connectionPool.getSubscriberCount(room);
      const connection = this.connectionPool.getConnection(room);

      stats.totalSubscribers += subscriberCount;
      stats.rooms[room] = {
        subscribers: subscriberCount,
        isConnected: connection?.isConnected || false,
        lastActivity: connection?.lastActivity || null,
        reconnectAttempts: this.reconnectAttempts.get(room) || 0,
      };
    }

    return stats;
  }

  // Get available room types
  getAvailableRoomTypes() {
    return Object.keys(STREAM_CONFIG).map((key) => ({
      type: key,
      displayName: STREAM_CONFIG[key].displayName,
      requiresParams: STREAM_CONFIG[key].requiresParams,
    }));
  }

  // Cleanup old transactions and perform maintenance
  performMaintenance() {
    // Clean up transaction deduplication set
    if (this.transactions.size > 10000) {
      const transactionsArray = Array.from(this.transactions);
      this.transactions.clear();
      transactionsArray.slice(-5000).forEach((tx) => this.transactions.add(tx));
      console.log(
        `🧹 [SolanaTracker] Cleaned up transaction deduplication set`
      );
    }

    // Clean up empty connections
    this.connectionPool.cleanup();

    // Clean up orphaned connections (connections with no subscribers)
    this.cleanupOrphanedConnections();

    console.log(`🔧 [SolanaTracker] Maintenance completed`);
  }

  // Clean up connections that have no subscribers (potential memory leaks)
  cleanupOrphanedConnections() {
    const activeRooms = this.connectionPool.getActiveRooms();
    let cleanedUp = 0;

    for (const room of activeRooms) {
      const subscriberCount = this.connectionPool.getSubscriberCount(room);
      const connection = this.connectionPool.getConnection(room);

      // If no subscribers but connection exists, clean it up
      if (subscriberCount === 0 && connection) {
        console.log(
          `🧹 [SolanaTracker] Cleaning up orphaned connection for room: ${room}`
        );
        this.connectionPool.removeConnection(room);
        cleanedUp++;
      }
    }

    if (cleanedUp > 0) {
      console.log(
        `🧹 [SolanaTracker] Cleaned up ${cleanedUp} orphaned connections`
      );
    }
  }

  // Bulk unsubscribe a subscriber from all rooms (for disconnections)
  async bulkUnsubscribe(subscriberId) {
    console.log(`🧹 [SolanaTracker] Bulk unsubscribing ${subscriberId} from all rooms`);

    const roomsToCleanup = [];

    // Find all rooms this subscriber is in
    for (const [room, subscribers] of this.connectionPool.subscribers) {
      if (subscribers.has(subscriberId)) {
        roomsToCleanup.push(room);
      }
    }

    console.log(`🔍 [SolanaTracker] Found ${roomsToCleanup.length} rooms to cleanup for subscriber ${subscriberId}`);

    // Unsubscribe from each room
    const results = [];
    for (const room of roomsToCleanup) {
      try {
        const result = await this.unsubscribe(room, subscriberId);
        results.push({ room, success: true, result });
      } catch (error) {
        console.error(`❌ [SolanaTracker] Failed to unsubscribe ${subscriberId} from room ${room}:`, error);
        results.push({ room, success: false, error: error.message });
      }
    }

    console.log(`✅ [SolanaTracker] Bulk unsubscription completed for ${subscriberId}: ${results.filter(r => r.success).length}/${results.length} successful`);

    return {
      subscriberId,
      totalRooms: roomsToCleanup.length,
      results
    };
  }

  // Bulk unsubscribe a subscriber from rooms matching a prefix (for parameterized stream bulk unsubscribe)
  async bulkUnsubscribeByPrefix(roomPrefix, subscriberId) {
    console.log(`🧹 [SolanaTracker] Bulk unsubscribing ${subscriberId} from rooms with prefix: ${roomPrefix}`);

    const roomsToCleanup = [];

    // Find all rooms this subscriber is in that match the prefix
    for (const [room, subscribers] of this.connectionPool.subscribers) {
      if (subscribers.has(subscriberId) && (room === roomPrefix || room.startsWith(`${roomPrefix}:`))) {
        roomsToCleanup.push(room);
      }
    }

    console.log(`🔍 [SolanaTracker] Found ${roomsToCleanup.length} rooms with prefix '${roomPrefix}' to cleanup for subscriber ${subscriberId}`);

    // Unsubscribe from each matching room
    const results = [];
    for (const room of roomsToCleanup) {
      try {
        const result = await this.unsubscribe(room, subscriberId);
        results.push({ room, success: true, result });
      } catch (error) {
        console.error(`❌ [SolanaTracker] Failed to unsubscribe ${subscriberId} from room ${room}:`, error);
        results.push({ room, success: false, error: error.message });
      }
    }

    console.log(`✅ [SolanaTracker] Bulk unsubscription by prefix completed for ${subscriberId}: ${results.filter(r => r.success).length}/${results.length} successful`);

    return {
      subscriberId,
      roomPrefix,
      unsubscribedRooms: roomsToCleanup.length,
      results
    };
  }
}
