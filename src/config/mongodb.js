/**
 * MongoDB Configuration Module
 *
 * Provides MongoDB connection management with intelligent connection pooling,
 * caching, and error handling for the StalkAPI system. Optimized for high-
 * performance data operations with automatic connection lifecycle management.
 *
 * Features:
 * - Global connection caching to prevent connection proliferation
 * - Intelligent connection pooling with configurable parameters
 * - Automatic connection health monitoring and recovery
 * - Environment-based configuration with production optimizations
 * - Graceful connection cleanup for application shutdown
 *
 * Collections Used:
 * - jupdca.daily_top_token_rankings - Smart money most bought tokens
 * - jupdca.daily_bottom_token_rankings - Smart money most sold tokens
 * - jupdca.daily_flows - SOL and memecoin flow data
 * - centraldata.token_metadata - Token symbols and metadata cache
 * - centraldata.stablecoins - Excluded stablecoin addresses
 * - centraldata.sol_derivative_tokens - Excluded SOL derivative tokens
 *
 * @module mongodb
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { MongoClient } from "mongodb";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * MongoDB Connection Pool Configuration
 *
 * Optimized for high-concurrency operations with intelligent
 * connection management and timeout handling.
 */
const dbPoolConfig = {
  min: 5,                           // Minimum connections to maintain
  max: 20,                          // Maximum concurrent connections
  acquireTimeoutMillis: 60000,      // Timeout for acquiring connections
  createTimeoutMillis: 30000,       // Timeout for creating new connections
  idleTimeoutMillis: 30000,         // Close idle connections after 30s
  reapIntervalMillis: 1000,         // Check for idle connections every 1s
  createRetryIntervalMillis: 200,   // Retry interval for failed connections
};

/**
 * MongoDB Connection Configuration
 */
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = "stalkreact"; // Default database name

// Validate required environment variables
if (!MONGODB_URI) {
  throw new Error("Missing required environment variable: MONGODB_URI");
}

/**
 * Global Connection Cache
 *
 * Maintains a cached connection across hot reloads in development
 * to prevent connection proliferation during API route usage.
 * This pattern is essential for serverless and development environments.
 */
let cached = global.mongo;

if (!cached) {
  cached = global.mongo = { conn: null, promise: null };
}

/**
 * Database Connection Function
 *
 * Establishes and caches MongoDB connection with comprehensive
 * error handling and event monitoring. Uses singleton pattern
 * to ensure only one connection per process.
 *
 * @async
 * @returns {Object} Connection object with client and db instances
 * @throws {Error} If connection fails after retries
 */
async function connectToDatabase() {
  // Return existing connection if available
  if (cached.conn) {
    return cached.conn;
  }

  // Create new connection promise if none exists
  if (!cached.promise) {
    const opts = {
      // Connection pool configuration
      maxPoolSize: dbPoolConfig.max,                    // Maximum concurrent connections
      minPoolSize: dbPoolConfig.min,                    // Minimum idle connections
      maxIdleTimeMS: dbPoolConfig.idleTimeoutMillis,    // Close idle connections after timeout
      waitQueueTimeoutMS: dbPoolConfig.acquireTimeoutMillis, // Queue timeout for connection requests
      connectTimeoutMS: dbPoolConfig.createTimeoutMillis,    // Initial connection timeout

      // Operation timeouts
      serverSelectionTimeoutMS: 5000,  // Server selection timeout
      socketTimeoutMS: 45000,          // Socket operation timeout

      // Reliability features
      retryWrites: true,               // Retry write operations on failure
      retryReads: true,                // Retry read operations on failure
    };

    // Create connection promise with event handlers
    cached.promise = MongoClient.connect(MONGODB_URI, opts).then((client) => {
      const db = client.db(MONGODB_DB);

      // Connection lifecycle event handlers
      client.on('open', () => {
        console.log('✅ MongoDB connection opened');
      });

      client.on('close', () => {
        console.log('🔌 MongoDB connection closed');
      });

      client.on('error', (error) => {
        console.error('❌ MongoDB connection error:', error.message);
      });

      client.on('timeout', () => {
        console.error('⏰ MongoDB connection timeout');
      });

      return {
        client,
        db,
      };
    });
  }

  // Await connection and cache result
  cached.conn = await cached.promise;
  return cached.conn;
}

// Get database instance with error handling
async function getDb(dbName = MONGODB_DB) {
  try {
    const { client } = await connectToDatabase();

    // Verify the connection is alive
    await client.db(dbName).command({ ping: 1 });

    return client.db(dbName);
  } catch (error) {
    console.error("Database connection error:", error);

    // If there's a connection issue, clear the cached connection
    if (cached.conn) {
      try {
        await cached.conn.client.close();
      } catch (closeError) {
        console.error("Error closing connection:", closeError);
      }
      cached.conn = null;
      cached.promise = null;
    }

    throw new Error(`Unable to connect to database: ${error.message}`);
  }
}

// Test MongoDB connection
export const testMongoConnection = async () => {
  try {
    const { client } = await connectToDatabase();

    // Test the connection with a ping
    await client.db(MONGODB_DB).command({ ping: 1 });

    console.log('✅ MongoDB connected successfully');
    return true;
  } catch (err) {
    console.error('❌ MongoDB connection failed:', err.message);
    return false;
  }
};

// MongoDB helper functions
export const mongodb = {
  // Get a collection
  getCollection: async (collectionName, dbName = MONGODB_DB) => {
    try {
      const db = await getDb(dbName);
      return db.collection(collectionName);
    } catch (error) {
      console.error('MongoDB get collection error:', error);
      throw error;
    }
  },

  // Insert one document
  insertOne: async (collectionName, document, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.insertOne(document);
      return result;
    } catch (error) {
      console.error('MongoDB insert error:', error);
      throw error;
    }
  },

  // Insert many documents
  insertMany: async (collectionName, documents, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.insertMany(documents);
      return result;
    } catch (error) {
      console.error('MongoDB insert many error:', error);
      throw error;
    }
  },

  // Find one document
  findOne: async (collectionName, query, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.findOne(query, options);
      return result;
    } catch (error) {
      console.error('MongoDB find one error:', error);
      throw error;
    }
  },

  // Find many documents
  find: async (collectionName, query = {}, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.find(query, options).toArray();
      return result;
    } catch (error) {
      console.error('MongoDB find error:', error);
      throw error;
    }
  },

  // Update one document
  updateOne: async (collectionName, filter, update, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.updateOne(filter, update, options);
      return result;
    } catch (error) {
      console.error('MongoDB update one error:', error);
      throw error;
    }
  },

  // Update many documents
  updateMany: async (collectionName, filter, update, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.updateMany(filter, update, options);
      return result;
    } catch (error) {
      console.error('MongoDB update many error:', error);
      throw error;
    }
  },

  // Delete one document
  deleteOne: async (collectionName, filter, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.deleteOne(filter);
      return result;
    } catch (error) {
      console.error('MongoDB delete one error:', error);
      throw error;
    }
  },

  // Delete many documents
  deleteMany: async (collectionName, filter, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.deleteMany(filter);
      return result;
    } catch (error) {
      console.error('MongoDB delete many error:', error);
      throw error;
    }
  },

  // Count documents
  countDocuments: async (collectionName, query = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.countDocuments(query);
      return result;
    } catch (error) {
      console.error('MongoDB count error:', error);
      throw error;
    }
  },

  // Create index
  createIndex: async (collectionName, indexSpec, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.createIndex(indexSpec, options);
      return result;
    } catch (error) {
      console.error('MongoDB create index error:', error);
      throw error;
    }
  },

  // Aggregate
  aggregate: async (collectionName, pipeline, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.aggregate(pipeline, options).toArray();
      return result;
    } catch (error) {
      console.error('MongoDB aggregate error:', error);
      throw error;
    }
  }
};

// Track if MongoDB has been closed
let mongoClosed = false;

// Graceful shutdown function
async function closeDatabase() {
  if (mongoClosed) {
    console.log('MongoDB connection already closed, skipping...');
    return;
  }

  if (cached.conn) {
    try {
      await cached.conn.client.close();
      cached.conn = null;
      cached.promise = null;
      mongoClosed = true;
      console.log('MongoDB connection closed');
    } catch (error) {
      console.error('Error closing MongoDB connection:', error.message);
      mongoClosed = true; // Mark as closed even if there was an error
    }
  } else {
    mongoClosed = true;
    console.log('MongoDB connection already closed');
  }
}

export { connectToDatabase, getDb, closeDatabase };
