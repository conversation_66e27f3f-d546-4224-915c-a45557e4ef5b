/**
 * PostgreSQL Database Configuration Module
 *
 * Provides connection pooling, query execution, and transaction management
 * for the StalkAPI PostgreSQL database. Optimized for high-concurrency
 * API workloads with intelligent connection pooling and monitoring.
 *
 * Features:
 * - Connection pooling with configurable min/max connections
 * - Automatic connection rotation to prevent stale connections
 * - Comprehensive connection monitoring and logging
 * - Environment-based configuration with sensible defaults
 * - Graceful connection cleanup for application shutdown
 *
 * @module database
 * <AUTHOR> Team
 * @version 1.0.0
 */

import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

/**
 * Database Connection Configuration
 *
 * Optimized for high-concurrency API workloads with intelligent
 * connection pooling and timeout management. All values can be
 * overridden via environment variables for different environments.
 */
const dbConfig = {
    // Connection parameters
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'api_engine',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',

    // SSL configuration - required for production databases
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,

    // Connection pool configuration - optimized for high concurrency
    max: parseInt(process.env.DB_MAX_CONNECTIONS) || 100, // Maximum concurrent connections
    min: parseInt(process.env.DB_MIN_CONNECTIONS) || 10,  // Minimum idle connections

    // Timeout configuration - balanced for performance and reliability
    idleTimeoutMillis: 30000,        // Close idle connections after 30 seconds
    connectionTimeoutMillis: 5000,   // Timeout for new connections
    acquireTimeoutMillis: 60000,     // Timeout for acquiring connections from pool

    // Connection lifecycle management
    allowExitOnIdle: true,            // Allow process to exit when all connections idle
    maxUses: 7500,                    // Rotate connections after 7500 uses to prevent staleness
};

/**
 * Connection Pool Instance
 *
 * Creates the main PostgreSQL connection pool used throughout the application.
 * The pool automatically manages connection lifecycle, scaling, and cleanup.
 */
const pool = new Pool(dbConfig);

/**
 * Connection Pool Event Handlers
 *
 * Provides comprehensive monitoring and logging of connection pool events
 * for debugging and performance optimization. Logging levels are adjusted
 * based on environment to reduce noise in production.
 */

// Monitor new connections being established
pool.on('connect', () => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Database connection established. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

// Monitor connection acquisition from pool (debug mode only)
pool.on('acquire', () => {
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DATABASE === 'true') {
        console.log(`📊 Database connection acquired. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

// Monitor connections being removed from pool
pool.on('remove', () => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`🗑️ Database connection removed. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

/**
 * Connection Pool Error Handler
 *
 * Handles unexpected errors on idle connections. In development, exits
 * the process immediately for debugging. In production, logs the error
 * but continues running to maintain service availability.
 */
pool.on('error', (err) => {
    console.error('❌ Unexpected error on idle client', err);

    // Exit immediately in development for debugging
    if (process.env.NODE_ENV !== 'production') {
        process.exit(-1);
    }
    // In production, log error but continue running
});

/**
 * Database Connection Test Function
 *
 * Tests the database connection by executing a simple query.
 * Used during application startup and health checks to verify
 * database connectivity.
 *
 * @async
 * @returns {boolean} True if connection successful, false otherwise
 */
export const testConnection = async () => {
    try {
        const client = await pool.connect();
        const result = await client.query('SELECT NOW()');
        client.release(); // Always release client back to pool

        console.log('✅ Database connected successfully at:', result.rows[0].now);
        return true;
    } catch (err) {
        console.error('❌ Database connection failed:', err.message);
        return false;
    }
};

/**
 * Query Execution Function
 *
 * Executes SQL queries with automatic error handling and optional
 * performance logging. Provides a consistent interface for all
 * database operations throughout the application.
 *
 * @async
 * @param {string} text - SQL query string with optional parameter placeholders
 * @param {Array} params - Query parameters for parameterized queries
 * @returns {Object} Query result object with rows, rowCount, etc.
 * @throws {Error} Database errors are re-thrown after logging
 */
export const query = async (text, params) => {
    const start = Date.now();

    try {
        const result = await pool.query(text, params);
        const duration = Date.now() - start;

        // Log query performance in development debug mode
        if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DATABASE === 'true') {
            console.log('Executed query', {
                text: text.substring(0, 100), // Truncate long queries for readability
                duration,
                rows: result.rowCount
            });
        }

        return result;
    } catch (error) {
        console.error('Database query error:', error);
        throw error; // Re-throw to allow caller to handle
    }
};

/**
 * Transaction Client Acquisition
 *
 * Gets a dedicated client from the pool for transaction operations.
 * The client must be manually released after transaction completion.
 *
 * @async
 * @returns {Object} PostgreSQL client for transaction operations
 * @example
 * const client = await getClient();
 * try {
 *   await client.query('BEGIN');
 *   // ... transaction operations
 *   await client.query('COMMIT');
 * } finally {
 *   client.release();
 * }
 */
export const getClient = async () => {
    return await pool.connect();
};

/**
 * Connection Pool Cleanup Function
 *
 * Gracefully closes the connection pool and all active connections.
 * Used during application shutdown to ensure clean database disconnection.
 * Includes safety checks to prevent multiple close attempts.
 *
 * @async
 * @throws {Error} If pool closure fails for reasons other than already closed
 */
export const closePool = async () => {
    // Safety check: verify pool exists and is not already closed
    if (!pool || pool.ended) {
        console.log('Database pool already closed, skipping...');
        return;
    }

    try {
        await pool.end(); // Gracefully close all connections
        console.log('✅ Database pool closed successfully');
    } catch (error) {
        // Handle expected "already closed" errors gracefully
        if (error.message.includes('Called end on pool more than once') ||
            error.message.includes('Pool was ended')) {
            console.log('Database pool already closed');
        } else {
            // Re-throw unexpected errors for proper error handling
            console.error('❌ Error closing database pool:', error.message);
            throw error;
        }
    }
};

/**
 * Export the connection pool as default export
 *
 * Allows direct access to the pool for advanced operations
 * while maintaining the recommended query() interface for most use cases.
 */
export default pool;
