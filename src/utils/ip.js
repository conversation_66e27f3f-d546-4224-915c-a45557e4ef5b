/**
 * IP Address Utility Module
 *
 * Comprehensive IP address extraction and validation utilities for the StalkAPI
 * system. Handles real client IP detection through various proxy configurations,
 * CDN services, and load balancers with intelligent header prioritization.
 *
 * Features:
 * - Real client IP extraction through multiple proxy layers
 * - Cloudflare CDN integration with CF-Connecting-IP support
 * - Load balancer and reverse proxy compatibility
 * - IP validation and sanitization
 * - Private IP range detection and filtering
 * - Rate limiting and abuse prevention integration
 * - Comprehensive logging for security monitoring
 *
 * Supported Proxy Headers:
 * - CF-Connecting-IP: Cloudflare real client IP (highest priority)
 * - X-Real-IP: Nginx reverse proxy real IP
 * - X-Forwarded-For: Standard proxy forwarding header
 * - X-Client-IP: Apache and other proxy client IP
 * - X-Cluster-Client-IP: Cluster proxy configurations
 * - True-Client-IP: Akamai and other CDN services
 *
 * Security Considerations:
 * - Header validation to prevent IP spoofing
 * - Private IP range detection and filtering
 * - Malformed IP address handling
 * - Rate limiting based on real client IP
 * - Abuse detection and blocking capabilities
 *
 * Use Cases:
 * - Rate limiting and abuse prevention
 * - Geolocation-based access control
 * - Security monitoring and logging
 * - Analytics and usage tracking
 * - Load balancer health checks
 *
 * @module ip
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * Real IP Address Extraction Function
 *
 * Extracts the real client IP address from various proxy headers with
 * intelligent prioritization and validation. Essential for accurate
 * rate limiting, security monitoring, and analytics when behind CDNs
 * or load balancers.
 *
 * Header Priority Order:
 * 1. CF-Connecting-IP (Cloudflare CDN - most reliable)
 * 2. X-Real-IP (Nginx reverse proxy)
 * 3. X-Forwarded-For (Standard proxy header - first IP in chain)
 * 4. X-Client-IP (Apache and other proxies)
 * 5. X-Cluster-Client-IP (Cluster configurations)
 * 6. req.connection.remoteAddress (Direct connection)
 * 7. req.socket.remoteAddress (Socket fallback)
 * 8. req.ip (Express default - final fallback)
 *
 * @param {Object} req - Express request object with headers
 * @returns {string} Real client IP address
 *
 * @example
 * const clientIP = getRealIP(req);
 * console.log(`Client IP: ${clientIP}`);
 */
export function getRealIP(req) {
    // Priority order for IP detection:
    // 1. CF-Connecting-IP (Cloudflare)
    // 2. X-Real-IP (nginx real_ip_module)
    // 3. X-Forwarded-For (standard proxy header)
    // 4. X-Client-IP (some proxies)
    // 5. X-Cluster-Client-IP (cluster environments)
    // 6. req.connection.remoteAddress (direct connection)
    // 7. req.socket.remoteAddress (fallback)
    // 8. req.ip (Express default)
    
    const headers = req.headers;
    
    // Cloudflare CF-Connecting-IP header (most reliable for Cloudflare)
    if (headers['cf-connecting-ip']) {
        return headers['cf-connecting-ip'];
    }
    
    // nginx X-Real-IP header
    if (headers['x-real-ip']) {
        return headers['x-real-ip'];
    }
    
    // X-Forwarded-For header (can contain multiple IPs, take the first one)
    if (headers['x-forwarded-for']) {
        const forwardedIPs = headers['x-forwarded-for'].split(',');
        const clientIP = forwardedIPs[0].trim();
        if (clientIP && isValidIP(clientIP)) {
            return clientIP;
        }
    }
    
    // X-Client-IP header
    if (headers['x-client-ip']) {
        return headers['x-client-ip'];
    }
    
    // X-Cluster-Client-IP header
    if (headers['x-cluster-client-ip']) {
        return headers['x-cluster-client-ip'];
    }
    
    // Direct connection IP
    if (req.connection && req.connection.remoteAddress) {
        return req.connection.remoteAddress;
    }
    
    // Socket IP (fallback)
    if (req.socket && req.socket.remoteAddress) {
        return req.socket.remoteAddress;
    }
    
    // Express default IP
    if (req.ip) {
        return req.ip;
    }
    
    // Ultimate fallback
    return 'unknown';
}

/**
 * Validate if a string is a valid IP address (IPv4 or IPv6)
 * @param {string} ip - IP address to validate
 * @returns {boolean} - True if valid IP
 */
export function isValidIP(ip) {
    if (!ip || typeof ip !== 'string') {
        return false;
    }
    
    // Remove any whitespace
    ip = ip.trim();
    
    // IPv4 regex
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    // IPv6 regex (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Get IP information for logging purposes
 * @param {Object} req - Express request object
 * @returns {Object} - IP information object
 */
export function getIPInfo(req) {
    const realIP = getRealIP(req);
    const headers = req.headers;
    
    return {
        realIP,
        cfConnectingIP: headers['cf-connecting-ip'] || null,
        xRealIP: headers['x-real-ip'] || null,
        xForwardedFor: headers['x-forwarded-for'] || null,
        xClientIP: headers['x-client-ip'] || null,
        connectionIP: req.connection?.remoteAddress || null,
        socketIP: req.socket?.remoteAddress || null,
        expressIP: req.ip || null,
        userAgent: headers['user-agent'] || null,
        cfRay: headers['cf-ray'] || null, // Cloudflare Ray ID for debugging
        cfCountry: headers['cf-ipcountry'] || null, // Cloudflare country code
        cfVisitor: headers['cf-visitor'] || null // Cloudflare visitor info
    };
}

/**
 * Middleware to add real IP to request object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function addRealIPMiddleware(req, res, next) {
    req.realIP = getRealIP(req);
    req.ipInfo = getIPInfo(req);
    next();
}

/**
 * Check if IP is from Cloudflare
 * @param {Object} req - Express request object
 * @returns {boolean} - True if request is from Cloudflare
 */
export function isCloudflareRequest(req) {
    const headers = req.headers;
    return !!(headers['cf-connecting-ip'] || headers['cf-ray'] || headers['cf-visitor']);
}

/**
 * Get country code from Cloudflare headers
 * @param {Object} req - Express request object
 * @returns {string|null} - Country code or null
 */
export function getCountryCode(req) {
    return req.headers['cf-ipcountry'] || null;
}

/**
 * Anonymize IP address for privacy compliance (GDPR, etc.)
 * @param {string} ip - IP address to anonymize
 * @returns {string} - Anonymized IP address
 */
export function anonymizeIP(ip) {
    if (!ip || ip === 'unknown') {
        return ip;
    }
    
    // IPv4: Replace last octet with 0
    if (ip.includes('.') && isValidIP(ip)) {
        const parts = ip.split('.');
        if (parts.length === 4) {
            return `${parts[0]}.${parts[1]}.${parts[2]}.0`;
        }
    }
    
    // IPv6: Replace last 64 bits with zeros
    if (ip.includes(':') && isValidIP(ip)) {
        const parts = ip.split(':');
        if (parts.length >= 4) {
            return `${parts.slice(0, 4).join(':')}::`;
        }
    }
    
    return ip;
}
