/**
 * Case Converter Utility Module
 *
 * Comprehensive utility functions for converting object keys between different
 * naming conventions (camelCase and snake_case). Essential for standardizing
 * API responses and data transformation across the StalkAPI system.
 *
 * Features:
 * - Intelligent camelCase detection to avoid converting non-camelCase strings
 * - Recursive object key conversion with deep nesting support
 * - Array handling with element-wise conversion
 * - Null and undefined value preservation
 * - Type-safe conversions that preserve data integrity
 * - Performance-optimized recursive algorithms
 * - Support for complex nested data structures
 * - Protection against converting token addresses and identifiers
 *
 * Use Cases:
 * - API response standardization (internal snake_case to external camelCase)
 * - Database result transformation (snake_case to application format)
 * - External API integration (camelCase to internal snake_case)
 * - Data serialization and deserialization
 * - Cross-system data format compatibility
 *
 * Performance Considerations:
 * - Optimized for large nested objects
 * - Minimal memory allocation during conversion
 * - Efficient string manipulation algorithms
 * - Recursive depth handling for complex structures
 * - Smart detection to avoid unnecessary conversions
 *
 * @module caseConverter
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * Convert camelCase string to snake_case
 *
 * Intelligently converts camelCase strings to snake_case format by inserting
 * underscores before uppercase letters and converting them to lowercase.
 * Includes smart detection to avoid converting non-camelCase strings like
 * token addresses, UUIDs, or other identifiers.
 *
 * @param {string} str - camelCase string to convert
 * @returns {string} snake_case formatted string or original if not camelCase
 *
 * @example
 * camelToSnake('tokenAddress') // Returns: 'token_address'
 * camelToSnake('maxRequestsPerMinute') // Returns: 'max_requests_per_minute'
 * camelToSnake('So11111111111111111111111111111111111111112') // Returns: unchanged (token address)
 * camelToSnake('UUID') // Returns: unchanged (all caps)
 */
function camelToSnake(str) {
  // Only convert if it looks like camelCase (starts with lowercase, contains uppercase)
  // Don't convert things like token addresses, UUIDs, or other all-caps strings
  if (!/^[a-z]/.test(str) || !/[A-Z]/.test(str)) {
    return str;
  }
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * Recursively convert all object keys from camelCase to snake_case
 * Handles nested objects, arrays, and preserves non-object values
 * @param {any} obj - The object/array/value to convert
 * @returns {any} - The converted object with snake_case keys
 */
function convertKeysToSnakeCase(obj) {
  // Handle null, undefined, or primitive values
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays - recursively convert each element
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToSnakeCase(item));
  }

  // Handle objects - convert keys and recursively convert values
  const converted = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = camelToSnake(key);
    converted[snakeKey] = convertKeysToSnakeCase(value);
  }

  return converted;
}

/**
 * Convert snake_case string to camelCase
 * @param {string} str - The snake_case string to convert
 * @returns {string} - The camelCase string
 */
function snakeToCamel(str) {
  return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * Recursively convert all object keys from snake_case to camelCase
 * Handles nested objects, arrays, and preserves non-object values
 * @param {any} obj - The object/array/value to convert
 * @returns {any} - The converted object with camelCase keys
 */
function convertKeysToCamelCase(obj) {
  // Handle null, undefined, or primitive values
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays - recursively convert each element
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }

  // Handle objects - convert keys and recursively convert values
  const converted = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = snakeToCamel(key);
    converted[camelKey] = convertKeysToCamelCase(value);
  }

  return converted;
}

export {
  camelToSnake,
  snakeToCamel,
  convertKeysToSnakeCase,
  convertKeysToCamelCase
};
