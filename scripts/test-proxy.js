import { getTokenPrice } from "../tools/coingecko.js";
import { getTokenPrice as getTokenPriceFromDexscreener } from "../tools/dexscreener.js";

async function main() {
  const tokenAddress = [
    "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump",
    "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
  ];
  const result = await getTokenPrice(tokenAddress);
  const result2 = await getTokenPriceFromDexscreener(tokenAddress); 
  console.log(JSON.stringify(result, null, 2));
  console.log(JSON.stringify(result2, null, 2));
}

main();
