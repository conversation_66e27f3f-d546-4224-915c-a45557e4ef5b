# WebSocket Streaming Guide

## Overview

StalkAPI provides real-time WebSocket streaming for live KOL (Key Opinion Leader) trading data. This guide covers connection, authentication, subscription management, and message handling.

## Connection Details

### Base WebSocket URL
```
ws://localhost:3001/ws          # Development
wss://your-domain.com/ws        # Production (SSL)
```

### Authentication

StalkAPI WebSocket connections use API key authentication:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');
```

For production:
```javascript
const ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');
```

## Available Streams

| Stream | Description | Credits per Message | Tier Required | Parameterized |
|--------|-------------|-------------------|---------------|---------------|
| `kol-feed` | Real-time KOL trading activity | 2 | Basic+ | No |
| `fresh-wallet-feed` | Real-time fresh wallet trading activity | 2 | Basic+ | No |
| `jupiter-amm-swaps` | Real-time Jupiter AMM swap data | 0 | Enterprise | No |
| `pumpfun-amm-swaps` | Real-time Pump.fun AMM swap data | 0 | Enterprise | No |
| `jupiter-dca-orders` | Real-time Jupiter DCA order data | 0 | Enterprise | No |
| `tokens-launched` | Newly launched tokens on Solana | 1 | Basic+ | No |
| `tokens-graduating` | Tokens graduating from bonding curves | 1 | Basic+ | Yes (`market_cap`) |
| `tokens-graduated` | Tokens that have graduated | 1 | Basic+ | No |
| `pool-changes` | Liquidity pool changes and updates | 1 | Basic+ | Yes (`pool_id`) |
| `token-transactions` | Token-specific transaction streams | 1 | Basic+ | Yes (`token`) |
| `price-updates` | Real-time token price updates | 1 | Basic+ | Yes (`token`) |
| `wallet-transactions` | Wallet-specific transaction streams | 1 | Basic+ | Yes (`wallet`) |
| `token-holders` | Token holder changes and updates | 1 | Basic+ | Yes (`token`) |
| `token-changes` | General token metadata changes | 1 | Basic+ | Yes (`token`) |

## Parameterized Streams

Some streams support parameters to filter data for specific tokens, wallets, or other criteria:

### Supported Parameters

- **`tokens-graduating`**: `market_cap` - Filter by market cap threshold (e.g., 175)
- **`pool-changes`**: `pool_id` - Filter by specific pool ID
- **`token-transactions`**: `token` - Filter by specific token mint address
- **`price-updates`**: `token` - Filter by specific token mint address
- **`wallet-transactions`**: `wallet` - Filter by specific wallet address
- **`token-holders`**: `token` - Filter by specific token mint address
- **`token-changes`**: `token` - Filter by specific token mint address

### Subscription Examples

```json
// Subscribe to all token transactions
{
  "type": "subscribe",
  "payload": {
    "stream": "token-transactions"
  }
}

// Subscribe to specific token transactions
{
  "type": "subscribe",
  "payload": {
    "stream": "token-transactions",
    "parameters": {
      "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    }
  }
}
```

## Message Protocol

### Client to Server Messages

#### Subscribe to Stream
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Unsubscribe from Stream

**Basic Unsubscribe (Non-parameterized streams):**
```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

**Parameterized Stream - Specific Parameter Unsubscribe:**
```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "token-transactions",
    "parameters": {
      "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    }
  }
}
```

**Parameterized Stream - Bulk Unsubscribe (All Parameters):**
```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "token-transactions"
  }
}
```

### Unsubscribe Behavior for Parameterized Streams

For parameterized streams, the unsubscribe behavior depends on whether parameters are provided:

1. **With Parameters**: Unsubscribes only from the specific parameterized subscription
   - Example: Unsubscribing from `token-transactions` with `token: "ABC123"` only stops receiving data for that specific token

2. **Without Parameters**: Unsubscribes from ALL parameterized subscriptions for that stream type
   - Example: Unsubscribing from `token-transactions` without parameters stops receiving data for all tokens you're subscribed to

**Response Format:**
```json
{
  "type": "unsubscribed",
  "stream": "token-transactions",
  "parameters": {"token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},
  "count": 1,
  "message": "Successfully unsubscribed from token-transactions with specified parameters"
}
```

For bulk unsubscribe:
```json
{
  "type": "unsubscribed",
  "stream": "token-transactions",
  "parameters": null,
  "count": 3,
  "message": "Successfully unsubscribed from 3 token-transactions subscriptions"
}
```

#### Ping (Keep Alive)
```json
{
  "type": "ping"
}
```

### Server to Client Messages

#### Connection Established
```json
{
  "type": "connected",
  "connectionId": "uuid-string",
  "sessionId": "uuid-string", 
  "message": "WebSocket connection established"
}
```

#### Subscription Confirmed
```json
{
  "type": "subscribed",
  "stream": "kol-feed",
  "message": "Successfully subscribed to kol-feed"
}
```

#### Unsubscription Confirmed
```json
{
  "type": "unsubscribed",
  "stream": "kol-feed",
  "message": "Successfully unsubscribed from kol-feed"
}
```

#### Pong Response
```json
{
  "type": "pong",
  "timestamp": 1748926077000
}
```

#### Stream Data
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748923344035,
    "type": "buy",
    "kol_label": "TraderSZ",
    "wallet": "********************************************",
    "kol_avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/trader_avatar.jpg",
    "token_in": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
      "amount": 13.0,
      "amount_string": "13.0",
      "amount_usd": 2095.16,
      "price": 161.17,
      "mint": "So11111111111111111111111111111111111111112"
    },
    "token_out": {
      "symbol": "BONK",
      "name": "Bonk",
      "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263/logo.png",
      "amount": 45200000,
      "amount_string": "45.2M",
      "amount_usd": 1987.45,
      "price": 0.000044,
      "mint": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
    },
    "signature": "5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr",
    "socials": [
      {
        "type": "x",
        "handle": "trader1sz",
        "followers": 658700
      }
    ]
  },
  "timestamp": 1748926077000
}
```

#### Error Messages
```json
{
  "type": "error",
  "error": "Access denied to stream: kol-feed",
  "timestamp": 1748926077000
}
```

#### Credit Warning
```json
{
  "type": "credit_warning",
  "message": "Insufficient credits for stream: kol-feed",
  "credits_remaining": 0,
  "credits_required": 2,
  "timestamp": 1748926077000
}
```

## Implementation Examples

### JavaScript/Node.js
```javascript
const WebSocket = require('ws');

// Connect with API key
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');

ws.on('open', () => {
    console.log('Connected to StalkAPI WebSocket');
    
    // Subscribe to KOL feed
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
});

ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    
    switch (message.type) {
        case 'connected':
            console.log('Connection established:', message.message);
            break;
            
        case 'subscribed':
            console.log(`Subscribed to: ${message.stream}`);
            break;
            
        case 'stream_data':
            if (message.stream === 'kol-feed') {
                console.log('KOL Trade:', {
                    trader: message.data.kol_label,
                    type: message.data.type,
                    tokenIn: message.data.token_in.symbol,
                    tokenOut: message.data.token_out.symbol,
                    amountUsd: message.data.token_in.amount_usd
                });
            }
            break;
            
        case 'error':
            console.error('WebSocket error:', message.error);
            break;
            
        case 'credit_warning':
            console.warn('Credit warning:', message.message);
            break;
    }
});

ws.on('error', (error) => {
    console.error('WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`WebSocket closed: ${code} - ${reason}`);
});

// Keep connection alive
setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
    }
}, 30000);
```

### Python
```python
import asyncio
import websockets
import json

async def connect_to_stalkapi():
    uri = "ws://localhost:3001/ws?apiKey=YOUR_API_KEY"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to StalkAPI WebSocket")
        
        # Subscribe to KOL feed
        await websocket.send(json.dumps({
            "type": "subscribe",
            "payload": {"stream": "kol-feed"}
        }))
        
        async for message in websocket:
            data = json.loads(message)
            
            if data["type"] == "stream_data" and data["stream"] == "kol-feed":
                trade = data["data"]
                print(f"KOL Trade: {trade['kol_label']} {trade['type']} "
                      f"{trade['token_in']['symbol']} -> {trade['token_out']['symbol']} "
                      f"(${trade['token_in']['amount_usd']:.2f})")
            
            elif data["type"] == "error":
                print(f"Error: {data['error']}")

# Run the client
asyncio.run(connect_to_stalkapi())
```

## Credit Management

### Credit Consumption
- **Connection**: Free (no credits consumed)
- **Stream Messages**: 2 credits per message for `kol-feed`
- **Insufficient Credits**: Connection remains open, but stream data is replaced with credit warnings

### Credit Monitoring
Use the admin endpoints to check remaining credits via the admin API.

## Best Practices

### 1. Connection Management
- Implement reconnection logic for production use
- Use ping/pong for keep-alive (every 30 seconds recommended)
- Handle connection errors gracefully

### 2. Message Handling
- Always parse JSON messages in try-catch blocks
- Implement proper error handling for all message types
- Log important events for debugging

### 3. Credit Optimization
- Monitor credit usage regularly
- Implement credit warnings handling
- Consider upgrading tier for high-volume usage

### 4. Security
- Use WSS (WebSocket Secure) in production
- Rotate API keys regularly
- Validate all incoming messages

## Troubleshooting

### Common Issues

#### Authentication Failed
```json
{"type": "error", "error": "Authentication failed"}
```
**Solution**: Check your API key validity

#### Access Denied to Stream
```json
{"type": "error", "error": "Access denied to stream: kol-feed"}
```
**Solution**: Verify your tier has access to the requested stream

#### Connection Drops
**Solution**: Implement reconnection logic with exponential backoff

### Testing WebSocket Connection

Use `wscat` for quick testing:
```bash
# Install wscat
npm install -g wscat

# Connect and test
wscat -c "ws://localhost:3001/ws?apiKey=YOUR_API_KEY"

# Send subscription message for KOL feed
{"type": "subscribe", "payload": {"stream": "kol-feed"}}

# Send subscription message for Premium streams
{"type": "subscribe", "payload": {"stream": "tokens-launched"}}
{"type": "subscribe", "payload": {"stream": "tokens-graduating"}}
{"type": "subscribe", "payload": {"stream": "tokens-graduated"}}
```

## Rate Limits

- **Connections per User**: Based on tier (Basic: 3, Premium: 10, Enterprise: 50)
- **Messages per Second**: No limit on client messages
- **Stream Data**: Real-time as available

## Support

For WebSocket-related issues:
1. Check connection logs
2. Verify authentication credentials  
3. Monitor credit balance
4. Review tier permissions
5. Contact support with connection details

---

**Next Steps**: 
- Try the [JavaScript example](#javascriptnodejs) 
- Check [available streams](#available-streams)
- Monitor [credit usage](#credit-management)
