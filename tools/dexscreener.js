import dotenv from "dotenv";
dotenv.config();

import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";

// https://api.dexscreener.com/tokens/v1/solana/

// Proxy configuration matching the working curl format
const proxyUrl = `http://${
  process.env.DATA_IMPULSE_USERNAME || "d36adf6a4d155703e205"
}:${process.env.DATA_IMPULSE_PASSWORD || "e7f34c5b037bbb5c"}@${
  process.env.DATA_IMPULSE_HOST || "gw.dataimpulse.com"
}:${parseInt(process.env.DATA_IMPULSE_PORT) || 823}`;

// Create proxy agent
const proxyAgent = new HttpsProxyAgent(proxyUrl);

const dexv1 = axios.create({
  baseURL: `https://api.dexscreener.com/tokens/v1`,
  timeout: 10000,
  httpsAgent: proxyAgent,
  proxy: false, // Disable axios built-in proxy since we're using httpsAgent
});

// Retry parameters
const MAX_RETRIES = 3;
const INITIAL_BACKOFF_MS = 50;
const BACKOFF_FACTOR = 2;

export async function getTokenPrice(tokenAddress, vsCurrency = "usd") {
  if (!Array.isArray(tokenAddress)) {
    tokenAddress = [tokenAddress];
  }
  if (tokenAddress.length > 25) {
    throw new Error("Too many tokens to fetch");
  }
  let attempt = 0;
  let backoff = INITIAL_BACKOFF_MS;
  while (attempt < MAX_RETRIES) {
    try {
      const response = await dexv1.get(
        `/solana/${tokenAddress.map((t) => t.trim()).join(",")}`
      );
      const result = tokenAddress.map((address) => ({
        tokenAddress: address.trim(),
        price: response.data.find((t) => t.baseToken.address === address.trim())
          ?.priceUsd,
        market_cap: response.data.find(
          (t) => t.baseToken.address === address.trim()
        )?.marketCap,
        "24h_volume": response.data.find(
          (t) => t.baseToken.address === address.trim()
        )?.volume.h24,
        "24h_price_change_percentage": response.data.find(
          (t) => t.baseToken.address === address.trim()
        )?.priceChange.h24,
      }));
      return result;
    } catch (error) {
      const status = error?.response?.status;
      // Only retry on network errors or 5xx errors
      if (
        (!status || (status >= 500 && status < 600)) &&
        attempt < MAX_RETRIES - 1
      ) {
        console.warn(
          `Retrying getTokenPrice (attempt ${attempt + 1}) after error:`,
          error.message || error
        );
        await new Promise((resolve) => setTimeout(resolve, backoff));
        backoff *= BACKOFF_FACTOR;
        attempt++;
        continue;
      } else {
        console.error("Error fetching token price from Gecko:", error);
        return null;
      }
    }
  }
}

export async function getTokenMetadata(tokenAddress) {
  /* 
  [
  {
        "chainId": "solana",
        "dexId": "pumpfun",
        "url": "https://dexscreener.com/solana/ba9r2ivejvaaccejdzeh9ykaec5trhnsznypc56c2tz8",
        "pairAddress": "BA9R2iVEJvAacCejdzeh9YKAeC5trhNSznyPC56C2TZ8",
        "baseToken": {
            "address": "Cr5nXmviX1w8Lf3yHTEBEEzBNzZika6aAKZEHEsc1gCh",
            "name": "Psychological Operations Group",
            "symbol": "POG"
        },
        "quoteToken": {
            "address": "So11111111111111111111111111111111111111112",
            "name": "Solana",
            "symbol": "SOL"
        },
        "priceNative": "0.00000007445",
        "priceUsd": "0.00001091",
        "txns": {
            "m5": {
                "buys": 2,
                "sells": 6
            },
            "h1": {
                "buys": 463,
                "sells": 431
            },
            "h6": {
                "buys": 2720,
                "sells": 2223
            },
            "h24": {
                "buys": 2720,
                "sells": 2223
            }
        },
        "volume": {
            "h24": 209356.48,
            "h6": 209356.48,
            "h1": 17439.04,
            "m5": 569.25
        },
        "priceChange": {
            "m5": -12.19,
            "h1": -1.72,
            "h6": 118,
            "h24": 118
        },
        "fdv": 10912.54,
        "marketCap": 10912.54,
        "pairCreatedAt": 1749857021000,
        "info": {
            "imageUrl": "https://dd.dexscreener.com/ds-data/tokens/solana/Cr5nXmviX1w8Lf3yHTEBEEzBNzZika6aAKZEHEsc1gCh.png?key=c3fdc7",
            "header": "https://dd.dexscreener.com/ds-data/tokens/solana/Cr5nXmviX1w8Lf3yHTEBEEzBNzZika6aAKZEHEsc1gCh/header.png?key=c3fdc7",
            "openGraph": "https://cdn.dexscreener.com/token-images/og/solana/Cr5nXmviX1w8Lf3yHTEBEEzBNzZika6aAKZEHEsc1gCh?timestamp=1749868800000",
            "websites": [],
            "socials": []
        }
    }
  ]
  */
}