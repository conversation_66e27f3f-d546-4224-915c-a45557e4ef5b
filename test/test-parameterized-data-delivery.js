/**
 * Test Suite for Parameterized Stream Data Delivery
 * 
 * Tests that parameterized stream subscriptions receive data correctly
 * when the data matches their subscription parameters.
 */

import { WebSocket } from 'ws';
import { setTimeout } from 'timers/promises';

// Test configuration
const WS_URL = 'ws://localhost:3001/ws';
const API_KEY = process.env.TEST_API_KEY || '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const TEST_WALLET = '********************************************';

class DataDeliveryTester {
    constructor() {
        this.ws = null;
        this.messages = [];
        this.connected = false;
        this.dataReceived = [];
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(`${WS_URL}?apiKey=${API_KEY}`);
            
            this.ws.on('open', () => {
                console.log('✅ Connected to WebSocket');
                this.connected = true;
                resolve();
            });

            this.ws.on('message', (data) => {
                const message = JSON.parse(data.toString());
                this.messages.push(message);
                
                // Track stream data messages separately
                if (message.type === 'stream_data') {
                    this.dataReceived.push(message);
                    console.log('📊 Stream data received:', {
                        stream: message.stream,
                        timestamp: message.timestamp,
                        dataKeys: Object.keys(message.data || {})
                    });
                } else {
                    console.log('📨 Message received:', message.type, message.stream || '');
                }
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });

            this.ws.on('close', () => {
                console.log('🔌 WebSocket disconnected');
                this.connected = false;
            });
        });
    }

    async send(message) {
        if (!this.connected) {
            throw new Error('WebSocket not connected');
        }
        
        console.log('📤 Sending:', JSON.stringify(message, null, 2));
        this.ws.send(JSON.stringify(message));
        await setTimeout(1000); // Wait for response
    }

    async subscribe(stream, parameters = null) {
        const payload = { stream };
        if (parameters) {
            payload.parameters = parameters;
        }
        
        await this.send({
            type: 'subscribe',
            payload
        });
    }

    getStreamDataCount(stream = null) {
        if (!stream) {
            return this.dataReceived.length;
        }
        return this.dataReceived.filter(msg => msg.stream === stream).length;
    }

    getLastStreamData(stream = null) {
        const filtered = stream 
            ? this.dataReceived.filter(msg => msg.stream === stream)
            : this.dataReceived;
        
        return filtered.length > 0 ? filtered[filtered.length - 1] : null;
    }

    clearDataReceived() {
        this.dataReceived = [];
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

async function testParameterizedDataDelivery() {
    console.log('🧪 Testing Parameterized Stream Data Delivery\n');
    
    const tester = new DataDeliveryTester();
    
    try {
        // Connect to WebSocket
        await tester.connect();
        await setTimeout(2000); // Wait for connection to stabilize
        
        console.log('\n=== Test 1: Subscribe to Parameterized Wallet Stream ===');
        
        // Subscribe to specific wallet transactions
        await tester.subscribe('wallet-transactions', { wallet: TEST_WALLET });
        
        console.log(`✅ Subscribed to wallet-transactions for wallet: ${TEST_WALLET}`);
        console.log('⏳ Waiting for data... (this may take a few minutes for real wallet activity)');
        
        // Wait for some time to see if data arrives
        const initialDataCount = tester.getStreamDataCount('wallet-transactions');
        console.log(`📊 Initial data count: ${initialDataCount}`);
        
        // Wait for 30 seconds to see if any data comes through
        for (let i = 0; i < 6; i++) {
            await setTimeout(5000);
            const currentDataCount = tester.getStreamDataCount('wallet-transactions');
            console.log(`📊 Data count after ${(i + 1) * 5}s: ${currentDataCount}`);
            
            if (currentDataCount > initialDataCount) {
                console.log('✅ Data received! Parameterized subscription is working.');
                const lastData = tester.getLastStreamData('wallet-transactions');
                console.log('📊 Last received data structure:', {
                    stream: lastData.stream,
                    timestamp: lastData.timestamp,
                    dataKeys: Object.keys(lastData.data || {}),
                    hasWalletField: !!(lastData.data?.wallet || lastData.data?.from_wallet || lastData.data?.to_wallet)
                });
                break;
            }
        }
        
        const finalDataCount = tester.getStreamDataCount('wallet-transactions');
        if (finalDataCount === initialDataCount) {
            console.log('ℹ️ No new data received during test period.');
            console.log('   This is normal if the wallet has no recent activity.');
            console.log('   The subscription setup appears to be working correctly based on the logs.');
        }
        
        console.log('\n=== Test 2: Subscribe to Multiple Parameterized Streams ===');
        
        // Subscribe to token transactions for a popular token (USDC)
        const USDC_TOKEN = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
        await tester.subscribe('token-transactions', { token: USDC_TOKEN });
        
        console.log(`✅ Subscribed to token-transactions for USDC: ${USDC_TOKEN}`);
        
        // Subscribe to price updates for the same token
        await tester.subscribe('price-updates', { token: USDC_TOKEN });
        
        console.log(`✅ Subscribed to price-updates for USDC: ${USDC_TOKEN}`);
        
        // Wait a bit more to see if we get any token-related data
        console.log('⏳ Waiting for token data...');
        
        const initialTokenDataCount = tester.getStreamDataCount('token-transactions');
        const initialPriceDataCount = tester.getStreamDataCount('price-updates');
        
        for (let i = 0; i < 4; i++) {
            await setTimeout(5000);
            const tokenDataCount = tester.getStreamDataCount('token-transactions');
            const priceDataCount = tester.getStreamDataCount('price-updates');
            
            console.log(`📊 After ${(i + 1) * 5}s - Token data: ${tokenDataCount}, Price data: ${priceDataCount}`);
            
            if (tokenDataCount > initialTokenDataCount || priceDataCount > initialPriceDataCount) {
                console.log('✅ Token-related data received! Parameterized subscriptions are working.');
                break;
            }
        }
        
        console.log('\n=== Test Summary ===');
        console.log(`📊 Total stream data messages received: ${tester.getStreamDataCount()}`);
        console.log(`📊 Wallet transactions: ${tester.getStreamDataCount('wallet-transactions')}`);
        console.log(`📊 Token transactions: ${tester.getStreamDataCount('token-transactions')}`);
        console.log(`📊 Price updates: ${tester.getStreamDataCount('price-updates')}`);
        
        if (tester.getStreamDataCount() > 0) {
            console.log('✅ Parameterized stream data delivery is working correctly!');
        } else {
            console.log('ℹ️ No data received during test period.');
            console.log('   This could be due to low activity for the subscribed parameters.');
            console.log('   The subscription mechanism appears to be set up correctly based on the connection logs.');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        tester.disconnect();
    }
}

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testParameterizedDataDelivery().catch(console.error);
}

export { DataDeliveryTester, testParameterizedDataDelivery };
