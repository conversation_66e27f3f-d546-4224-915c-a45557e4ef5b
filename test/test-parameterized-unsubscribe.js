/**
 * Test Suite for Parameterized Stream Unsubscribe Functionality
 * 
 * Tests the enhanced unsubscribe functionality for parameterized streams
 * including specific parameter unsubscribe and bulk stream unsubscribe.
 */

import { WebSocket } from 'ws';
import { setTimeout } from 'timers/promises';

// Test configuration
const WS_URL = 'ws://localhost:3001/ws';
const API_KEY = process.env.TEST_API_KEY || '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const TEST_TOKEN_1 = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
const TEST_TOKEN_2 = 'So11111111111111111111111111111111111111112'; // SOL
const TEST_WALLET = '********************************************';

class WebSocketTester {
    constructor() {
        this.ws = null;
        this.messages = [];
        this.connected = false;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(`${WS_URL}?apiKey=${API_KEY}`);
            
            this.ws.on('open', () => {
                console.log('✅ Connected to WebSocket');
                this.connected = true;
                resolve();
            });

            this.ws.on('message', (data) => {
                const message = JSON.parse(data.toString());
                this.messages.push(message);
                console.log('📨 Received:', JSON.stringify(message, null, 2));
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });

            this.ws.on('close', () => {
                console.log('🔌 WebSocket disconnected');
                this.connected = false;
            });
        });
    }

    async send(message) {
        if (!this.connected) {
            throw new Error('WebSocket not connected');
        }
        
        console.log('📤 Sending:', JSON.stringify(message, null, 2));
        this.ws.send(JSON.stringify(message));
        await setTimeout(1000); // Wait for response
    }

    async subscribe(stream, parameters = null) {
        const payload = { stream };
        if (parameters) {
            payload.parameters = parameters;
        }
        
        await this.send({
            type: 'subscribe',
            payload
        });
    }

    async unsubscribe(stream, parameters = null) {
        const payload = { stream };
        if (parameters) {
            payload.parameters = parameters;
        }
        
        await this.send({
            type: 'unsubscribe',
            payload
        });
    }

    getLastMessage(type = null) {
        if (!type) {
            return this.messages[this.messages.length - 1];
        }
        
        for (let i = this.messages.length - 1; i >= 0; i--) {
            if (this.messages[i].type === type) {
                return this.messages[i];
            }
        }
        return null;
    }

    clearMessages() {
        this.messages = [];
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

async function runTests() {
    console.log('🧪 Starting Parameterized Stream Unsubscribe Tests\n');
    
    const tester = new WebSocketTester();
    
    try {
        // Connect to WebSocket
        await tester.connect();
        await setTimeout(2000); // Wait for connection to stabilize
        
        console.log('\n=== Test 1: Subscribe to Multiple Parameterized Streams ===');
        
        // Subscribe to multiple token-transactions with different parameters
        await tester.subscribe('token-transactions', { token: TEST_TOKEN_1 });
        await tester.subscribe('token-transactions', { token: TEST_TOKEN_2 });
        
        // Subscribe to wallet-transactions
        await tester.subscribe('wallet-transactions', { wallet: TEST_WALLET });
        
        // Subscribe to price-updates
        await tester.subscribe('price-updates', { token: TEST_TOKEN_1 });
        
        console.log('\n=== Test 2: Specific Parameter Unsubscribe ===');
        tester.clearMessages();
        
        // Unsubscribe from specific token-transactions
        await tester.unsubscribe('token-transactions', { token: TEST_TOKEN_1 });
        
        const specificUnsubResponse = tester.getLastMessage('unsubscribed');
        if (specificUnsubResponse) {
            console.log('✅ Specific unsubscribe response received');
            console.log(`   Stream: ${specificUnsubResponse.stream}`);
            console.log(`   Parameters: ${JSON.stringify(specificUnsubResponse.parameters)}`);
            console.log(`   Count: ${specificUnsubResponse.count}`);
            
            if (specificUnsubResponse.count === 1 && specificUnsubResponse.parameters) {
                console.log('✅ Specific parameter unsubscribe working correctly');
            } else {
                console.log('❌ Specific parameter unsubscribe failed');
            }
        } else {
            console.log('❌ No unsubscribe response received');
        }
        
        console.log('\n=== Test 3: Bulk Stream Unsubscribe ===');
        tester.clearMessages();
        
        // Bulk unsubscribe from all token-transactions (should unsubscribe from TEST_TOKEN_2)
        await tester.unsubscribe('token-transactions');
        
        const bulkUnsubResponse = tester.getLastMessage('unsubscribed');
        if (bulkUnsubResponse) {
            console.log('✅ Bulk unsubscribe response received');
            console.log(`   Stream: ${bulkUnsubResponse.stream}`);
            console.log(`   Parameters: ${JSON.stringify(bulkUnsubResponse.parameters)}`);
            console.log(`   Count: ${bulkUnsubResponse.count}`);
            
            if (bulkUnsubResponse.count >= 1 && !bulkUnsubResponse.parameters) {
                console.log('✅ Bulk stream unsubscribe working correctly');
            } else {
                console.log('❌ Bulk stream unsubscribe failed');
            }
        } else {
            console.log('❌ No bulk unsubscribe response received');
        }
        
        console.log('\n=== Test 4: Non-Parameterized Stream Unsubscribe ===');
        tester.clearMessages();
        
        // Subscribe to non-parameterized stream
        await tester.subscribe('kol-feed');
        await setTimeout(1000);
        
        // Unsubscribe from non-parameterized stream
        await tester.unsubscribe('kol-feed');
        
        const nonParamUnsubResponse = tester.getLastMessage('unsubscribed');
        if (nonParamUnsubResponse) {
            console.log('✅ Non-parameterized unsubscribe response received');
            console.log(`   Stream: ${nonParamUnsubResponse.stream}`);
            console.log(`   Count: ${nonParamUnsubResponse.count}`);
            
            if (nonParamUnsubResponse.count === 1) {
                console.log('✅ Non-parameterized stream unsubscribe working correctly');
            } else {
                console.log('❌ Non-parameterized stream unsubscribe failed');
            }
        } else {
            console.log('❌ No non-parameterized unsubscribe response received');
        }
        
        console.log('\n=== Test 5: Remaining Subscriptions Cleanup ===');
        tester.clearMessages();
        
        // Clean up remaining subscriptions
        await tester.unsubscribe('wallet-transactions');
        await tester.unsubscribe('price-updates');
        
        console.log('✅ Cleanup completed');
        
        console.log('\n🎉 All tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        tester.disconnect();
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(console.error);
}

export { WebSocketTester, runTests };
