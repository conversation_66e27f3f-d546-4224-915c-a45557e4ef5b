import { getTokenMetadata } from '../tools/dexscreener.js';

// Test tokens - using well-known Solana tokens
const TEST_TOKENS = [
    'So11111111111111111111111111111111111111112', // SOL
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
    '4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump'  // DADDY TATE
];

async function testDexScreenerMetadata() {
    console.log('🧪 Testing DexScreener Token Metadata Implementation');
    console.log('=' .repeat(60));

    try {
        // Test 1: Single token
        console.log('\n1️⃣ Testing single token metadata...');
        const singleResult = await getTokenMetadata(TEST_TOKENS[0]);
        
        if (singleResult && singleResult.length > 0) {
            console.log('✅ Single token test successful');
            console.log('📊 Token data:', JSON.stringify(singleResult[0], null, 2));
        } else {
            console.log('❌ Single token test failed - no data returned');
        }

        // Test 2: Multiple tokens
        console.log('\n2️⃣ Testing multiple tokens metadata...');
        const multipleResult = await getTokenMetadata(TEST_TOKENS);
        
        if (multipleResult && multipleResult.length > 0) {
            console.log('✅ Multiple tokens test successful');
            console.log(`📊 Found ${multipleResult.length} tokens out of ${TEST_TOKENS.length} requested`);
            multipleResult.forEach((token, index) => {
                console.log(`   Token ${index + 1}:`, {
                    address: token.tokenAddress,
                    name: token.name,
                    symbol: token.symbol,
                    logo: token.logo ? 'Present' : 'Null'
                });
            });
        } else {
            console.log('❌ Multiple tokens test failed - no data returned');
        }

        // Test 3: Non-existent token
        console.log('\n3️⃣ Testing non-existent token...');
        const nonExistentResult = await getTokenMetadata(['InvalidTokenAddress123']);
        
        if (!nonExistentResult || nonExistentResult.length === 0) {
            console.log('✅ Non-existent token test successful - correctly returned empty result');
        } else {
            console.log('❌ Non-existent token test failed - should return empty result');
        }

        // Test 4: Cache test (run same query again)
        console.log('\n4️⃣ Testing cache functionality...');
        const cacheResult = await getTokenMetadata(TEST_TOKENS[0]);
        
        if (cacheResult && cacheResult.length > 0) {
            console.log('✅ Cache test successful - data retrieved from cache/DB');
            console.log('📊 Cached token data:', {
                address: cacheResult[0].tokenAddress,
                name: cacheResult[0].name,
                symbol: cacheResult[0].symbol
            });
        } else {
            console.log('❌ Cache test failed');
        }

        console.log('\n🎉 DexScreener metadata tests completed!');

    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

// Run the test
testDexScreenerMetadata();
