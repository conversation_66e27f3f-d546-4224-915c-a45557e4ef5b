/**
 * StalkAPI - Professional KOL Trading Data API Engine
 *
 * A comprehensive API platform providing real-time access to cryptocurrency trading
 * activity from verified KOL (Key Opinion Leader) traders. Features include:
 * - Real-time WebSocket streaming with credit-based usage tracking
 * - Multi-tier access control with rate limiting
 * - Smart money analytics and token metadata caching
 * - Kafka stream processing for high-throughput data ingestion
 * - Redis-backed caching and pub/sub messaging
 * - PostgreSQL and MongoDB data persistence
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @license ISC
 */

// Core Express.js framework and essential middleware
import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import compression from "compression";
import { createServer } from "http";
import dotenv from "dotenv";

// Database and caching configuration modules
import { testConnection, closePool } from "./src/config/database.js";
import { testRedisConnection, closeRedis } from "./src/config/redis.js";
import { testMongoConnection, closeDatabase } from "./src/config/mongodb.js";
import { closeCentralConnection } from "./src/config/redisCentral.js";

// Authentication and utility middleware
import { optionalAuth } from "./src/middleware/auth.js";
import { addRealIPMiddleware } from "./src/utils/ip.js";
import { errorHandler, jsonErrorHandler, notFoundHandler } from "./src/middleware/errorHandler.js";

// API route modules organized by functionality
import authRoutes from "./src/routes/auth.js";
import apiRoutes from "./src/routes/api.js";
import websocketRoutes from "./src/routes/websocket.js";
import adminRoutes from "./src/routes/admin.js";
import docsRoutes from "./src/routes/docs.js";

// Real-time streaming and data processing components
import { WSServer } from "./src/websocket/WebSocketServer.js";
import { StreamManager } from "./src/websocket/StreamManager.js";
import DataManager from "./src/dataManager.js";

// Load environment variables from .env file
dotenv.config();

// Initialize Express application and server configuration
const app = express();
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || "localhost";

// Create HTTP server instance for WebSocket upgrade support
const server = createServer(app);

/**
 * Security Middleware Configuration
 *
 * Implements comprehensive security headers and policies to protect against
 * common web vulnerabilities including XSS, CSRF, clickjacking, and more.
 * Configuration is environment-aware for development vs production.
 */
app.use(
  helmet({
    // Content Security Policy - Controls resource loading to prevent XSS
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"], // Only allow resources from same origin by default
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"], // Allow inline styles for documentation
        fontSrc: ["'self'", "https://fonts.gstatic.com"], // Allow Google Fonts
        scriptSrc: [
          "'self'",
          "'unsafe-eval'", // Required for API documentation tools
          "blob:", // Required for WebSocket and worker scripts
          "https://cdn.jsdelivr.net", // CDN for documentation assets
        ],
        imgSrc: ["'self'", "data:", "https:"], // Allow images from any HTTPS source
        connectSrc: ["'self'", "wss:", "ws:"], // Allow WebSocket connections
        workerSrc: ["'self'", "blob:"], // Allow web workers
        frameSrc: ["'none'"], // Prevent embedding in frames
        objectSrc: ["'none'"], // Prevent object/embed tags
        baseUri: ["'self'"], // Restrict base tag usage
        formAction: ["'self'"], // Restrict form submissions
        // Upgrade HTTP to HTTPS in production only
        upgradeInsecureRequests:
          process.env.NODE_ENV === "production" ? [] : null,
      },
    },

    // Cross-Origin policies for API compatibility
    crossOriginEmbedderPolicy: false, // Disabled for third-party integrations
    crossOriginOpenerPolicy: { policy: "same-origin" },
    crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin API access

    // HTTP Strict Transport Security - Force HTTPS in production
    hsts:
      process.env.NODE_ENV === "production"
        ? {
            maxAge: 31536000, // 1 year in seconds
            includeSubDomains: true, // Apply to all subdomains
            preload: true, // Include in browser preload lists
          }
        : false, // Disabled in development for localhost testing

    // Additional security headers
    noSniff: true, // Prevent MIME type sniffing
    frameguard: { action: "deny" }, // Prevent clickjacking
    xssFilter: true, // Enable XSS filtering
    referrerPolicy: { policy: "strict-origin-when-cross-origin" }, // Control referrer information
  })
);

/**
 * CORS (Cross-Origin Resource Sharing) Configuration
 *
 * Configures allowed origins, methods, and headers for cross-origin requests.
 * Production restricts to official domain, development allows localhost testing.
 */
app.use(
  cors({
    // Environment-specific origin allowlist
    origin:
      process.env.NODE_ENV === "production"
        ? ["https://stalkapi.com"] // Production: Only official domain
        : ["http://localhost:3000", "http://localhost:3001"], // Development: Local testing
    credentials: true, // Allow cookies and authorization headers
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"], // Supported HTTP methods
    allowedHeaders: ["Content-Type", "Authorization", "X-API-Key"], // Required headers for API access
  })
);

/**
 * Response Compression Middleware
 *
 * Automatically compresses response bodies for improved performance.
 * Reduces bandwidth usage and improves response times for large payloads.
 */
app.use(compression());

/**
 * HTTP Request Logging
 *
 * Uses Morgan logger in 'combined' format for detailed request logging.
 * Disabled during testing to reduce noise in test output.
 */
if (process.env.NODE_ENV !== "test") {
  app.use(morgan("combined"));
}

/**
 * Request Body Parsing Middleware
 *
 * Configures JSON and URL-encoded body parsing with generous size limits
 * to accommodate large data payloads and batch operations.
 */
app.use(express.json({ limit: "10mb" })); // Parse JSON bodies up to 10MB
app.use(express.urlencoded({ extended: true, limit: "10mb" })); // Parse form data up to 10MB

/**
 * JSON Parsing Error Handler
 *
 * Must be placed after express.json() to catch JSON parsing errors
 * and return user-friendly error messages instead of generic 400 errors.
 */
app.use(jsonErrorHandler);

/**
 * Proxy Trust Configuration
 *
 * Enables Express to trust proxy headers for accurate client IP detection.
 * Critical for rate limiting and security when behind Cloudflare or load balancers.
 */
app.set("trust proxy", true);

/**
 * Real IP Address Extraction
 *
 * Custom middleware to extract real client IP from Cloudflare headers
 * (CF-Connecting-IP) and other proxy headers for accurate rate limiting.
 */
app.use(addRealIPMiddleware);

/**
 * Static File Serving
 *
 * Serves documentation assets, API reference files, and other static content
 * from the public directory for the documentation interface.
 */
app.use(express.static("public"));

/**
 * Health Check Endpoint
 *
 * Provides comprehensive system health status including database connections,
 * Redis cache, MongoDB, system uptime, and memory usage. Used for monitoring
 * and load balancer health checks.
 *
 * @route GET /health
 * @returns {Object} Health status with service details and system metrics
 */
app.get("/health", async (_req, res) => {
  try {
    // Test all critical service connections in parallel for faster response
    const [dbHealthy, redisHealthy, mongoHealthy] = await Promise.all([
      testConnection(),
      testRedisConnection(),
      testMongoConnection(),
    ]);

    const allHealthy = dbHealthy && redisHealthy && mongoHealthy;

    const health = {
      status: allHealthy ? "healthy" : "unhealthy",
      timestamp: new Date().toISOString(),
      services: {
        database: dbHealthy ? "healthy" : "unhealthy", // PostgreSQL connection status
        redis: redisHealthy ? "healthy" : "unhealthy", // Redis cache/pub-sub status
        mongodb: mongoHealthy ? "healthy" : "unhealthy", // MongoDB connection status
      },
      uptime: process.uptime(), // Process uptime in seconds
      memory: process.memoryUsage(), // Node.js memory usage statistics
      version: "1.0.0", // API version
    };

    // Return 200 if all services healthy, 503 if any service is down
    res.status(allHealthy ? 200 : 503).json(health);
  } catch (error) {
    // Return 503 Service Unavailable for any health check errors
    res.status(503).json({
      status: "unhealthy",
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * API Route Registration
 *
 * Mounts route modules at their respective base paths.
 * Order matters for middleware execution and route matching.
 */
app.use("/auth", authRoutes); // Authentication and user management
app.use("/api/v1", apiRoutes); // Main API endpoints with versioning
app.use("/ws-api", websocketRoutes); // WebSocket connection management
app.use("/admin", adminRoutes); // Administrative functions
app.use("/docs", docsRoutes); // API documentation interface

/**
 * Root API Endpoint
 *
 * Provides API discovery information including available endpoints,
 * WebSocket connection details, and basic API metadata. Supports
 * optional authentication to show user-specific information.
 *
 * @route GET /
 * @middleware optionalAuth - Adds user info if API key provided
 * @returns {Object} API discovery information and endpoint directory
 */
app.get("/", optionalAuth, (req, res) => {
  const welcomeMessage = {
    message: "StalkApi", // API name and branding
    version: "1.0.0", // Current API version
    timestamp: new Date().toISOString(), // Response timestamp
    endpoints: {
      authentication: "/auth", // User authentication and management
      api: "/api/v1", // Main API endpoints with versioning
      websocket_info: "/ws-api", // WebSocket connection management
      health: "/health", // System health monitoring
      documentation: "/docs", // Interactive API documentation
    },
    websocket: {
      endpoint: `wss://${req.get("host")}/ws`, // WebSocket connection URL
      authentication: "API key required", // Authentication requirement
    },
  };

  res.json(welcomeMessage);
});

/**
 * 404 Not Found Handler
 *
 * Catches all unmatched routes and returns a structured error response
 * with helpful information about available endpoints.
 */
app.use("*", notFoundHandler);

/**
 * Global Error Handler
 *
 * Must be the last middleware in the chain. Catches all unhandled errors
 * and returns sanitized error responses to prevent information leakage.
 */
app.use(errorHandler);

/**
 * Service Initialization Function
 *
 * Initializes all critical services in the correct order:
 * 1. Database connections (PostgreSQL, Redis, MongoDB)
 * 2. WebSocket server for real-time communication
 * 3. Stream manager for data processing workers
 * 4. Data manager for scheduled data aggregation
 *
 * Stores service references in app.locals for graceful shutdown.
 * Exits process if any critical service fails to initialize.
 *
 * @async
 * @function initializeServices
 * @throws {Error} If any critical service fails to initialize
 */
async function initializeServices() {
  try {
    console.log("🚀 Starting API Engine...");

    // Test all database connections in parallel for faster startup
    const [dbConnected, redisConnected, mongoConnected] = await Promise.all([
      testConnection(), // PostgreSQL connection test
      testRedisConnection(), // Redis cache/pub-sub test
      testMongoConnection(), // MongoDB connection test
    ]);

    // Validate all database connections are healthy
    if (!dbConnected) {
      throw new Error("PostgreSQL database connection failed");
    }

    if (!redisConnected) {
      throw new Error("Redis connection failed");
    }

    if (!mongoConnected) {
      throw new Error("MongoDB connection failed");
    }

    // Initialize WebSocket server for real-time communication
    const wsServer = new WSServer(server);
    console.log("✅ WebSocket server initialized");

    // Initialize and start stream manager for data processing workers
    const streamManager = new StreamManager();
    await streamManager.start();
    console.log("✅ Stream manager started");

    // Start data manager for scheduled data aggregation tasks
    const dataManager = new DataManager();
    await dataManager.initialize();
    console.log("✅ Data manager initialized");

    // Store service references in app.locals for graceful shutdown access
    app.locals.wsServer = wsServer;
    app.locals.streamManager = streamManager;
    app.locals.dataManager = dataManager;

    console.log("✅ All services initialized successfully");
  } catch (error) {
    console.error("❌ Service initialization failed:", error);
    process.exit(1); // Exit with error code if initialization fails
  }
}

/**
 * Server Startup Function
 *
 * Initializes all services and starts the HTTP server.
 * Provides comprehensive startup logging with service URLs and environment info.
 *
 * @async
 * @function startServer
 * @throws {Error} If server fails to start or services fail to initialize
 */
async function startServer() {
  try {
    // Initialize all services before starting the server
    await initializeServices();

    // Start HTTP server and bind to configured host/port
    server.listen(PORT, HOST, () => {
      console.log(`🌟 API Engine running on http://${HOST}:${PORT}`);
      console.log(`🔌 WebSocket server available at ws://${HOST}:${PORT}/ws`);
      console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
      console.log(`📚 API Documentation: http://${HOST}:${PORT}/docs`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1); // Exit with error code if startup fails
  }
}

/**
 * Graceful Shutdown Signal Handlers
 *
 * Registers handlers for SIGTERM (Docker/Kubernetes) and SIGINT (Ctrl+C)
 * to ensure clean shutdown of all services and database connections.
 */
process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

/**
 * Graceful Shutdown Function
 *
 * Performs orderly shutdown of all services:
 * 1. Stop accepting new HTTP connections
 * 2. Close WebSocket server and active connections
 * 3. Stop stream processing workers
 * 4. Stop data aggregation workers
 * 5. Close all database connections
 *
 * @async
 * @function gracefulShutdown
 * @param {string} signal - The signal that triggered shutdown (SIGTERM/SIGINT)
 */
async function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

  try {
    // Stop accepting new HTTP connections
    server.close(() => {
      console.log("✅ HTTP server closed");
    });

    // Stop WebSocket server first to prevent new connections
    if (app.locals.wsServer) {
      await app.locals.wsServer.shutdown();
      console.log("✅ WebSocket server stopped");
    }

    // Stop stream processing workers
    if (app.locals.streamManager) {
      await app.locals.streamManager.stop();
      console.log("✅ Stream manager stopped");
    }

    // Stop data aggregation workers
    if (app.locals.dataManager) {
      await app.locals.dataManager.stop();
      console.log("✅ Data manager stopped");
    }

    // Close all database connections in parallel
    await Promise.all([
      closePool(), // PostgreSQL connection pool
      closeRedis(), // Redis cache and pub/sub connections
      closeDatabase(), // MongoDB connection
      closeCentralConnection(), // Central Redis connection
    ]);

    console.log("✅ Graceful shutdown completed");
    process.exit(0); // Exit successfully
  } catch (error) {
    console.error("❌ Error during shutdown:", error);
    process.exit(1); // Exit with error code if shutdown fails
  }
}

/**
 * Global Error Handlers
 *
 * Catches unhandled exceptions and promise rejections to prevent
 * silent failures and ensure proper logging before process termination.
 */

// Handle uncaught exceptions (synchronous errors not caught by try/catch)
process.on("uncaughtException", (error) => {
  console.error("❌ Uncaught Exception:", error);
  console.error("Stack trace:", error.stack);
  process.exit(1); // Exit immediately for uncaught exceptions
});

// Handle unhandled promise rejections (async errors without .catch())
process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  console.error("This may indicate a missing .catch() handler");
  process.exit(1); // Exit immediately for unhandled rejections
});

/**
 * Application Startup
 *
 * Starts the server only when not in test mode to prevent
 * conflicts during automated testing.
 */
if (process.env.NODE_ENV !== "test") {
  startServer();
}

// Export the Express app for testing and external use
export default app;
